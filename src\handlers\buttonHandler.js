import { logger } from '../utils/logger.js';
import { <PERSON>bed<PERSON><PERSON>er, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder, ChannelSelectMenuBuilder, ChannelType, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder } from 'discord.js';
import BotConfig from '../models/BotConfig.js';
import Product from '../models/Product.js';
import Store from '../models/Store.js';
import StockItem from '../models/StockItem.js';
import { BotLogger } from '../utils/botLogger.js';
import { rateLimiter } from '../utils/rateLimiter.js';
import ShoppingCartHandler from './shoppingCartHandler.js';
import CartButtonHandler from './cartButtonHandler.js';
import { StoreButtonHandler } from './buttons/storeButtonHandler.js';
import { AdminButtonHandler } from './buttons/admin<PERSON><PERSON>onHandler.js';
import { ConfigButtonHandler } from './buttons/configButtonHandler.js';
import { ActionButtonHandler } from './buttons/actionButtonHandler.js';
import { COLORS } from '../config/constants.js';
import { getEmojiFromInteraction } from '../utils/emojiHelper.js';
import { pixPaymentManager } from '../utils/pixPaymentManager.js';

/**
 * Manipula interações de botões
 * @param {ButtonInteraction} interaction 
 */
export async function handleButton(interaction) {
    const customId = interaction.customId;
    
    try {
        logger.info(`Botão clicado: ${customId} por ${interaction.user.tag}`);

        // Roteamento baseado no customId do botão
        // Para config: config_admin_logs, config_public_logs, etc.
        // Para outros: categoria_acao_id
        const parts = customId.split('_');
        const category = parts[0];
        
        let action, id;
        
        if (category === 'config') {
            // Para botões de config, junta todas as partes após 'config_' como action
            action = parts.slice(1).join('_');
            id = null;
        } else {
            // Para outros botões, mantém o formato original
            action = parts[1];
            id = parts[2];
        }

        switch (category) {
            case 'store':
                await StoreButtonHandler.handleStoreButton(interaction, action, id);
                break;
            case 'admin':
                await AdminButtonHandler.handleAdminButton(interaction, action, id);
                break;
            case 'debug':
                await ActionButtonHandler.handleDebugButton(interaction, action, id);
                break;
            case 'delete':
                await ActionButtonHandler.handleDeleteButton(interaction, action, id);
                break;
            case 'config':
                await ConfigButtonHandler.handleConfigButton(interaction, action, id);
                break;
            case 'confirm':
                await ActionButtonHandler.handleConfirmButton(interaction, action, customId);
                break;
            case 'cancel':
                await ActionButtonHandler.handleCancelButton(interaction, action, customId);
                break;
            case 'view':
                await ActionButtonHandler.handleViewButton(interaction, action, customId);
                break;
            case 'search':
                await ActionButtonHandler.handleSearchButton(interaction, action, customId);
                break;
            case 'cart':
                // Verifica se é um dos novos botões do carrinho fixo
                if (['cart_clear', 'cart_checkout', 'cart_cancel_payment', 'cart_check_payment_status', 'cart_manage_items', 'cart_confirm_cancel', 'cart_dismiss_cancel', 'cart_cancel_purchase', 'cart_copy_pix'].includes(customId)) {
                    await CartButtonHandler.handleCartButton(interaction);
                } else {
                    // Botões antigos do carrinho
                    await ShoppingCartHandler.handleCartButton(interaction);
                }
                break;
            default:
                // Verifica se é um botão específico do carrinho
                if (customId === 'cart_confirm_purchase') {
                    await ShoppingCartHandler.handleConfirmPurchase(interaction);
                } else {
                    logger.warn(`Categoria de botão não reconhecida: ${category}`);
                    await interaction.reply({
                        content: '❌ Botão não reconhecido.',
                        ephemeral: true
                    });
                }
        }

    } catch (error) {
        logger.error(`Erro ao processar botão ${customId}:`, error);
        
        try {
            const errorMessage = {
                content: '❌ Erro ao processar a ação do botão.',
                ephemeral: true
            };

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply(errorMessage);
            } else if (interaction.deferred) {
                await interaction.editReply({
                    content: '❌ Erro ao processar a ação do botão.'
                });
            } else if (interaction.replied) {
                await interaction.followUp(errorMessage);
            }
        } catch (replyError) {
            logger.error(`Erro ao responder após falha no botão ${customId}:`, replyError);
        }
    }
}





/**
 * Exibe informações de debug das lojas
 */
async function handleStoresDebug(interaction) {
    try {
        const Store = (await import('../models/Store.js')).default;

        // Busca todas as lojas do servidor
        const stores = await Store.find({ guildId: interaction.guild.id });

        if (stores.length === 0) {
            return await interaction.reply({
                content: '🐛 **Debug - Lojas**\n\n❌ Nenhuma loja encontrada neste servidor.',
                ephemeral: true
            });
        }

        let debugInfo = '🐛 **Debug - Informações das Lojas**\n\n';

        stores.forEach((store, index) => {
            debugInfo += `**${index + 1}. ${store.name}**\n`;
            debugInfo += `• ID: \`${store._id}\`\n`;
            debugInfo += `• Canal ID: \`${store.channelId}\`\n`;
            debugInfo += `• Mensagem ID: \`${store.messageId || 'N/A'}\`\n`;
            debugInfo += `• Ativa: ${store.isActive ? '✅' : '❌'}\n`;
            debugInfo += `• Criada: ${store.createdAt.toLocaleString('pt-BR')}\n`;
            debugInfo += `• Atualizada: ${store.updatedAt.toLocaleString('pt-BR')}\n`;
            debugInfo += `• Cor: \`${store.color}\`\n`;
            debugInfo += `• Banner: ${store.banner ? '✅' : '❌'}\n\n`;
        });

        // Limita o tamanho da mensagem
        if (debugInfo.length > 1900) {
            debugInfo = debugInfo.substring(0, 1900) + '\n\n... (truncado)';
        }

        await interaction.reply({
            content: debugInfo,
            ephemeral: true
        });

        logger.info(`Debug de lojas executado por ${interaction.user.tag} em ${interaction.guild.name}`);

    } catch (error) {
        logger.error('Erro ao executar debug de lojas:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar informações de debug das lojas.',
            ephemeral: true
        });
    }
}

/**
 * Confirma e executa a deleção de uma loja
 */
async function handleDeleteStoreConfirm(interaction, storeId) {
    try {
        // Defer a resposta pois a deleção pode demorar
        await interaction.deferReply({ ephemeral: true });

        const Store = (await import('../models/Store.js')).default;
        const { StoreManager } = await import('../utils/storeManager.js');

        // Busca a loja
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.editReply({
                content: '❌ Loja não encontrada ou já foi deletada.'
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.editReply({
                content: '❌ Esta loja não pertence a este servidor.'
            });
        }

        // Executa a deleção
        const result = await StoreManager.deleteStore(interaction.guild, store, interaction.user);

        if (result.success) {
            await interaction.editReply({
                content: `✅ ${result.message}\n\n` +
                        `**Detalhes da loja deletada:**\n` +
                        `• Nome: ${result.deletedStore.name}\n` +
                        `• Canal ID: ${result.deletedStore.channelId}\n` +
                        `• ID da Loja: \`${result.deletedStore.id}\``
            });

            logger.info(`Loja "${result.deletedStore.name}" deletada com sucesso por ${interaction.user.tag} em ${interaction.guild.name}`);
        } else {
            await interaction.editReply({
                content: `❌ ${result.message}`
            });

            logger.error(`Falha ao deletar loja: ${result.error}`);
        }

    } catch (error) {
        logger.error('Erro ao confirmar deleção de loja:', error);

        const errorMessage = {
            content: '❌ Erro ao deletar a loja. Tente novamente mais tarde.'
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(errorMessage);
        } else {
            await interaction.reply({ ...errorMessage, ephemeral: true });
        }
    }
}









/**
 * Configura canais de log estruturados
 */
async function handleLogChannelsConfig(interaction) {
    const embed = new EmbedBuilder()
        .setColor(COLORS.PRIMARY)
        .setTitle(`${EMOJIS.INFO} Configurar Canais de Log`)
        .setDescription('Selecione o tipo de canal de log que deseja configurar:')
        .addFields(
            {
                name: '🔧 Canais Principais',
                value: '• **Admin** - Logs administrativos\n• **Público** - Logs públicos (vendas)\n• **Erro** - Logs de erro',
                inline: true
            },
            {
                name: '🛡️ Canais de Segurança',
                value: '• **Moderação** - Logs de segurança\n• **Debug** - Logs de debug',
                inline: true
            },
            {
                name: '⚙️ Canais de Sistema',
                value: '• **Sistema** - Logs do sistema\n• **Comandos** - Logs de comandos\n• **Eventos** - Logs de eventos\n• **Banco** - Logs de banco\n• **API** - Logs de APIs',
                inline: true
            }
        );

    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('config_select_log_type')
        .setPlaceholder('Selecione um tipo de log para configurar')
        .addOptions(
            {
                label: '🔧 Admin - Logs administrativos',
                description: 'Configurações, criação de lojas, ações admin',
                value: 'admin'
            },
            {
                label: '📢 Público - Logs públicos',
                description: 'Vendas, estatísticas públicas, anúncios',
                value: 'public'
            },
            {
                label: '❌ Erro - Logs de erro',
                description: 'Erros do sistema, falhas críticas',
                value: 'error'
            },
            {
                label: '🛡️ Moderação - Logs de segurança',
                description: 'Atividades suspeitas, rate limiting',
                value: 'moderation'
            },
            {
                label: '⚙️ Sistema - Logs do sistema',
                description: 'Inicialização, conexões, status',
                value: 'system'
            },
            {
                label: '🐛 Debug - Logs de debug',
                description: 'Informações detalhadas para debug',
                value: 'debug'
            },
            {
                label: '⚡ Comandos - Logs de comandos',
                description: 'Execução de comandos, performance',
                value: 'commands'
            },
            {
                label: '📡 Eventos - Logs de eventos',
                description: 'Eventos do Discord, interações',
                value: 'events'
            },
            {
                label: '🗄️ Banco - Logs de banco de dados',
                description: 'Operações de banco, queries',
                value: 'database'
            },
            {
                label: '🌐 API - Logs de APIs externas',
                description: 'Integrações, webhooks, APIs',
                value: 'api'
            }
        );

    const row = new ActionRowBuilder().addComponents(selectMenu);

    await interaction.reply({
        embeds: [embed],
        components: [row],
        ephemeral: true
    });
}

/**
 * Configura configurações gerais de logging
 */
async function handleLogSettingsConfig(interaction) {
    const config = await BotConfig.findByGuild(interaction.guild.id);

    const modal = new ModalBuilder()
        .setCustomId('config_log_settings_modal')
        .setTitle('Configurações de Log');

    const logLevelInput = new TextInputBuilder()
        .setCustomId('log_level')
        .setLabel('Nível de Log Discord (ERROR/WARN/INFO/DEBUG)')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('WARN')
        .setValue(config?.logSettings?.discordLogLevel || 'WARN')
        .setRequired(true)
        .setMaxLength(5);

    const stackTraceInput = new TextInputBuilder()
        .setCustomId('stack_trace')
        .setLabel('Incluir Stack Trace em erros (true/false)')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('true')
        .setValue(config?.logSettings?.includeStackTrace?.toString() || 'true')
        .setRequired(true)
        .setMaxLength(5);

    const row1 = new ActionRowBuilder().addComponents(logLevelInput);
    const row2 = new ActionRowBuilder().addComponents(stackTraceInput);

    modal.addComponents(row1, row2);

    await interaction.showModal(modal);
}

/**
 * Testa todos os canais de log configurados
 */
async function handleTestLogs(interaction) {
    await interaction.deferReply({ ephemeral: true });

    try {
        const config = await BotConfig.findByGuild(interaction.guild.id);
        if (!config || !config.logChannels) {
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            return await interaction.editReply({
                content: `${errorEmoji} Nenhum canal de log configurado para testar.`
            });
        }

        const { botLogger } = await import('../utils/botLogger.js');

        let testesSucesso = 0;
        let testesTotal = 0;

        // Testa cada tipo de canal configurado
        const logTypes = Object.keys(config.logChannels).filter(key => config.logChannels[key]);

        for (const tipo of logTypes) {
            testesTotal++;
            try {
                await botLogger.logToDiscord('INFO', 'SYSTEM', `🧪 Teste de log do tipo: ${tipo}`, {
                    guildId: interaction.guild.id,
                    userId: interaction.user.id
                }, {
                    teste: true,
                    executadoPor: interaction.user.tag,
                    timestamp: new Date().toISOString()
                });
                testesSucesso++;
            } catch (error) {
                await logger.logStructured('WARN', 'COMMAND', `Falha no teste de log ${tipo}`, {
                    guildId: interaction.guild.id
                }, {
                    error: error.message,
                    tipo
                });
            }
        }

        const successEmoji = await getEmojiFromInteraction(interaction, 'SUCCESS');
        const embed = new EmbedBuilder()
            .setColor(testesSucesso === testesTotal ? COLORS.SUCCESS : COLORS.WARNING)
            .setTitle(`${successEmoji} Teste de Logs Concluído`)
            .setDescription(`**${testesSucesso}/${testesTotal}** canais testados com sucesso`)
            .addFields({
                name: '📊 Resultados',
                value: `Tipos testados: ${logTypes.join(', ')}`,
                inline: false
            })
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        await logger.logStructured('ERROR', 'COMMAND', 'Erro ao testar logs', {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            error: error.message
        });

        const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
        await interaction.editReply({
            content: `${errorEmoji} Erro ao executar teste de logs.`
        });
    }
}

/**
 * Manipula botões de confirmação
 */
async function handleConfirmButton(interaction, action, customId) {
    // Verificação de permissões
    if (!interaction.member.permissions.has('Administrator')) {
        return await interaction.reply({
            content: '❌ Apenas administradores podem confirmar esta ação.',
            ephemeral: true
        });
    }

    if (action === 'delete' && customId.includes('stock')) {
        await handleConfirmDeleteStock(interaction, customId);
    } else if (action === 'delete' && customId.includes('product')) {
        await handleConfirmDeleteProduct(interaction, customId);
    } else {
        await interaction.reply({
            content: '❌ Ação de confirmação não reconhecida.',
            ephemeral: true
        });
    }
}

/**
 * Manipula botões de cancelamento
 */
async function handleCancelButton(interaction, action, customId) {
    if (action === 'delete' && customId.includes('stock')) {
        await interaction.update({
            content: '❌ Deleção de estoque cancelada.',
            components: [],
            embeds: [],
            ephemeral: true
        });
    } else if (action === 'delete' && customId.includes('product')) {
        await interaction.update({
            content: '❌ Deleção de produto cancelada.',
            components: [],
            embeds: [],
            ephemeral: true
        });
    } else {
        await interaction.update({
            content: '❌ Operação cancelada.',
            components: [],
            embeds: [],
            ephemeral: true
        });
    }
}

/**
 * Confirma e executa a deleção de itens de estoque
 */
async function handleConfirmDeleteStock(interaction, customId) {
    try {
        // Defer a resposta pois a deleção pode demorar
        await interaction.deferReply({ ephemeral: true });

        // Parse do customId: confirm_delete_stock_storeId_productId_itemIds
        const parts = customId.split('_');
        const storeId = parts[3];
        const productId = parts[4];
        const itemIds = parts.slice(5).join('_').split(',');

        // Importa os modelos necessários
        const StockItem = (await import('../models/StockItem.js')).default;
        const Product = (await import('../models/Product.js')).default;
        const Store = (await import('../models/Store.js')).default;

        // Busca informações do produto e loja
        const product = await Product.findById(productId);
        const store = await Store.findById(storeId);

        if (!product || !store) {
            return await interaction.editReply({
                content: '❌ Produto ou loja não encontrados.'
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.editReply({
                content: '❌ Esta loja não pertence a este servidor.'
            });
        }

        // Busca os itens que serão deletados
        const itemsToDelete = await StockItem.find({
            _id: { $in: itemIds },
            status: 'available'
        });

        if (itemsToDelete.length === 0) {
            return await interaction.editReply({
                content: '❌ Nenhum item disponível encontrado para deleção.'
            });
        }

        // Executa a deleção
        const deleteResult = await StockItem.deleteMany({
            _id: { $in: itemIds },
            status: 'available'
        });

        // Atualiza o contador de estoque do produto
        const remainingStock = await StockItem.countDocuments({
            productId: productId,
            status: 'available'
        });

        await Product.findByIdAndUpdate(productId, {
            stock: remainingStock,
            status: remainingStock > 0 ? 'active' : 'out_of_stock'
        });

        // Resposta de sucesso
        const embed = new EmbedBuilder()
            .setTitle('✅ Estoque Deletado com Sucesso')
            .setDescription(`**Produto:** ${product.name}\n**Loja:** ${store.name}`)
            .setColor('#00ff00')
            .addFields(
                {
                    name: '🗑️ Itens Deletados',
                    value: `${deleteResult.deletedCount} item(ns)`,
                    inline: true
                },
                {
                    name: '📦 Estoque Restante',
                    value: `${remainingStock} item(ns)`,
                    inline: true
                }
            )
            .setFooter({ text: `Deletado por ${interaction.user.tag}` })
            .setTimestamp();

        await interaction.editReply({
            embeds: [embed]
        });

        logger.info(`${deleteResult.deletedCount} itens de estoque deletados do produto "${product.name}" por ${interaction.user.tag} em ${interaction.guild.name}`);

    } catch (error) {
        logger.error('Erro ao deletar itens de estoque:', error);

        await interaction.editReply({
            content: '❌ Erro ao deletar itens de estoque. Tente novamente mais tarde.'
        });
    }
}

/**
 * Manipula botões relacionados à visualização
 */
async function handleViewButton(interaction, action, customId) {
    // Parse do customId para botões de paginação: view_stock_page_productId_pageNumber
    if (customId.startsWith('view_stock_page_')) {
        await handleStockPaginationButton(interaction, customId);
        return;
    }

    await interaction.reply({
        content: '❌ Ação de visualização não reconhecida.',
        ephemeral: true
    });
}

/**
 * Manipula botões de paginação de estoque
 */
async function handleStockPaginationButton(interaction, customId) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem visualizar estoque.',
                ephemeral: true
            });
        }

        // Parse do customId: view_stock_page_productId_pageNumber
        const parts = customId.split('_');
        const productId = parts[3];
        const pageNumber = parseInt(parts[4]);

        // Busca o produto
        const product = await Product.findById(productId);
        if (!product) {
            return await interaction.reply({
                content: '❌ Produto não encontrado.',
                ephemeral: true
            });
        }

        // Busca a loja
        const store = await Store.findById(product.storeId);
        if (!store) {
            return await interaction.reply({
                content: '❌ Loja não encontrada.',
                ephemeral: true
            });
        }

        // Busca todos os itens de estoque do produto
        const stockItems = await StockItem.find({ productId }).sort({ createdAt: -1 });
        const stockSummary = await StockItem.getStockSummary(productId);

        // Configuração da paginação
        const availableItems = stockItems.filter(item => item.status === 'available');
        const itemsPerPage = 10;
        const totalPages = Math.ceil(availableItems.length / itemsPerPage);
        const currentPage = Math.max(1, Math.min(pageNumber, totalPages));

        // Cria embed com informações detalhadas do estoque
        const embed = new EmbedBuilder()
            .setTitle(`📦 Estoque Detalhado - ${product.name}`)
            .setDescription(`**Loja:** ${store.name}\n**Preço:** R$ ${product.price.toFixed(2)}`)
            .setColor('#0099ff')
            .setTimestamp();

        // Adiciona resumo do estoque
        embed.addFields({
            name: '📊 Resumo do Estoque',
            value: `🟢 **Disponível:** ${stockSummary.available}\n` +
                   `🔴 **Vendido:** ${stockSummary.sold}\n` +
                   `🟡 **Reservado:** ${stockSummary.reserved}\n` +
                   `⚫ **Expirado:** ${stockSummary.expired}\n` +
                   `📈 **Total:** ${stockSummary.total}`,
            inline: true
        });

        // Mostra itens da página atual
        if (stockSummary.available > 0) {
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageItems = availableItems.slice(startIndex, endIndex);

            const itemsList = pageItems.map((item, index) => {
                const content = item.content.length > 50 ?
                    item.content.substring(0, 50) + '...' :
                    item.content;
                const globalIndex = startIndex + index + 1;
                return `${globalIndex}. \`${content}\``;
            }).join('\n');

            embed.addFields({
                name: `🔍 Itens Disponíveis (Página ${currentPage}/${totalPages})`,
                value: itemsList || 'Nenhum item disponível',
                inline: false
            });

            if (totalPages > 1) {
                embed.addFields({
                    name: '📄 Navegação',
                    value: `Página ${currentPage} de ${totalPages} • Total: ${stockSummary.available} itens`,
                    inline: false
                });
            }
        }

        // Botões para ações adicionais e navegação
        const actionButtons = new ActionRowBuilder();
        const navigationButtons = new ActionRowBuilder();

        if (stockSummary.available > 0) {
            actionButtons.addComponents(
                new ButtonBuilder()
                    .setCustomId(`admin_edit_stock_${productId}`)
                    .setLabel('✏️ Editar Estoque')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId(`admin_delete_stock_${productId}`)
                    .setLabel('🗑️ Deletar Itens')
                    .setStyle(ButtonStyle.Danger)
            );
        }

        actionButtons.addComponents(
            new ButtonBuilder()
                .setCustomId(`admin_add_stock_${productId}`)
                .setLabel('➕ Adicionar Estoque')
                .setStyle(ButtonStyle.Success)
        );

        // Adiciona botões de navegação se há múltiplas páginas
        if (totalPages > 1) {
            navigationButtons.addComponents(
                new ButtonBuilder()
                    .setCustomId(`view_stock_page_${productId}_1`)
                    .setLabel('⏮️ Primeira')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(currentPage === 1),
                new ButtonBuilder()
                    .setCustomId(`view_stock_page_${productId}_${Math.max(1, currentPage - 1)}`)
                    .setLabel('◀️ Anterior')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(currentPage === 1),
                new ButtonBuilder()
                    .setCustomId(`view_stock_page_${productId}_${Math.min(totalPages, currentPage + 1)}`)
                    .setLabel('▶️ Próxima')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(currentPage === totalPages),
                new ButtonBuilder()
                    .setCustomId(`view_stock_page_${productId}_${totalPages}`)
                    .setLabel('⏭️ Última')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(currentPage === totalPages)
            );
        }

        const components = [actionButtons];
        if (navigationButtons.components.length > 0) {
            components.push(navigationButtons);
        }

        await interaction.update({
            embeds: [embed],
            components: components,
            ephemeral: true
        });

        logger.info(`Página ${currentPage} do estoque do produto ${product.name} visualizada por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar paginação de estoque:', error);

        const errorMessage = {
            content: '❌ Erro interno do servidor. Tente novamente mais tarde.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

/**
 * Manipula botões relacionados à busca
 */
async function handleSearchButton(interaction, action, customId) {
    // Parse do customId para botões de busca: search_stock_productId
    if (customId.startsWith('search_stock_')) {
        await handleStockSearchButton(interaction, customId);
        return;
    }

    await interaction.reply({
        content: '❌ Ação de busca não reconhecida.',
        ephemeral: true
    });
}

/**
 * Manipula botão de busca no estoque
 */
async function handleStockSearchButton(interaction, customId) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem buscar no estoque.',
                ephemeral: true
            });
        }

        // Parse do customId: search_stock_productId
        const productId = customId.replace('search_stock_', '');

        // Busca o produto
        const product = await Product.findById(productId);
        if (!product) {
            return await interaction.reply({
                content: '❌ Produto não encontrado.',
                ephemeral: true
            });
        }

        // Cria modal de busca
        const modal = new ModalBuilder()
            .setCustomId(`stock_search_modal_${productId}`)
            .setTitle(`🔍 Buscar no Estoque - ${product.name}`);

        // Campo de busca
        const searchInput = new TextInputBuilder()
            .setCustomId('search_term')
            .setLabel('Termo de Busca')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Digite o termo para buscar no conteúdo dos itens...')
            .setRequired(true)
            .setMaxLength(100);

        // Campo de filtro de status (opcional)
        const statusInput = new TextInputBuilder()
            .setCustomId('status_filter')
            .setLabel('Filtrar por Status (opcional)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('available, sold, reserved, expired (deixe vazio para todos)')
            .setRequired(false)
            .setMaxLength(20);

        const firstRow = new ActionRowBuilder().addComponents(searchInput);
        const secondRow = new ActionRowBuilder().addComponents(statusInput);

        modal.addComponents(firstRow, secondRow);

        await interaction.showModal(modal);

        logger.info(`Modal de busca no estoque do produto ${product.name} aberto por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar botão de busca no estoque:', error);

        const errorMessage = {
            content: '❌ Erro interno do servidor. Tente novamente mais tarde.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

/**
 * Confirma e executa a deleção de um produto
 */
async function handleConfirmDeleteProduct(interaction, customId) {
    try {
        // Extrai o ID do produto do customId
        const productId = customId.split('_')[3];
        const guildId = interaction.guild.id;

        // Busca o produto
        const product = await Product.findOne({ _id: productId, status: { $ne: 'discontinued' } }).populate('storeId');
        if (!product || product.storeId.guildId !== guildId) {
            return await interaction.update({
                content: '❌ Produto não encontrado ou inativo.',
                components: [],
                embeds: [],
                ephemeral: true
            });
        }

        // Conta itens de estoque antes da deleção
        const stockCount = await StockItem.countDocuments({ 
            productId, 
            status: 'available' 
        });

        // Remove todos os itens de estoque do produto
        const deletedStockResult = await StockItem.deleteMany({ productId });

        // Marca o produto como inativo
        product.status = 'inactive';
        await product.save();

        // Atualiza o contador de produtos na loja
        await Store.findByIdAndUpdate(
            product.storeId._id,
            { $inc: { productCount: -1 } }
        );

        // Log da ação
        await BotLogger.logAction({
            guildId,
            userId: interaction.user.id,
            action: 'PRODUCT_DELETE',
            details: {
                productId: product._id,
                productName: product.name,
                storeId: product.storeId._id,
                storeName: product.storeId.name,
                deletedStockItems: deletedStockResult.deletedCount,
                availableStockCount: stockCount
            }
        });

        // Cria embed de confirmação
        const embed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setTitle('✅ Produto Removido com Sucesso')
            .setDescription(`O produto **${product.name}** foi removido permanentemente da loja **${product.storeId.name}**.`)
            .addFields(
                { name: '🛍️ Produto Removido', value: product.name, inline: true },
                { name: '🏪 Loja', value: product.storeId.name, inline: true },
                { name: '💰 Preço', value: product.price, inline: true },
                { name: '📦 Itens de Estoque Removidos', value: `${deletedStockResult.deletedCount} itens`, inline: true },
                { name: '📅 Data da Remoção', value: new Date().toLocaleDateString('pt-BR'), inline: true },
                { name: '👤 Removido por', value: interaction.user.toString(), inline: true }
            )
            .setFooter({ text: 'Esta ação foi registrada nos logs do sistema.' })
            .setTimestamp();

        await interaction.update({
            content: '',
            embeds: [embed],
            components: [],
            ephemeral: true
        });

        logger.info(`Produto removido: ${product.name} (${productId}) da loja ${product.storeId.name} por ${interaction.user.tag}. ${deletedStockResult.deletedCount} itens de estoque removidos.`);

    } catch (error) {
        logger.error('Erro ao confirmar deleção de produto:', error);
        await interaction.update({
            content: '❌ Erro interno do servidor ao remover o produto. Tente novamente mais tarde.',
            components: [],
            embeds: [],
            ephemeral: true
        });
    }
}

/**
 * Testa a configuração do MercadoPago
 */
async function handleTestMercadoPago(interaction) {
    try {
        await interaction.deferReply({ ephemeral: true });

        logger.info(`Testando configuração do MercadoPago para guild ${interaction.guild.id} por ${interaction.user.tag}`);

        // Testa a conectividade com a API
        const testResult = await pixPaymentManager.testApiConnection(interaction.guild.id);

        let embed;

        if (testResult.success) {
            embed = new EmbedBuilder()
                .setColor(COLORS.SUCCESS)
                .setTitle(`${EMOJIS.SUCCESS} Teste do MercadoPago - Sucesso`)
                .setDescription(testResult.message)
                .addFields(
                    {
                        name: '🌍 Ambiente',
                        value: testResult.environment === 'sandbox' ? '🧪 Sandbox (Teste)' : '🚀 Produção',
                        inline: true
                    },
                    {
                        name: '🔑 Tipo de Token',
                        value: testResult.details?.tokenType || 'Não identificado',
                        inline: true
                    },
                    {
                        name: '📡 Status da API',
                        value: testResult.details?.status || 'Conectado',
                        inline: true
                    }
                )
                .setTimestamp();

            // Adiciona aviso se estiver em sandbox
            if (testResult.environment === 'sandbox') {
                embed.addFields({
                    name: '⚠️ Aviso',
                    value: 'Você está usando tokens de **teste (sandbox)**. Os pagamentos não serão reais.',
                    inline: false
                });
            }

        } else {
            embed = new EmbedBuilder()
                .setColor(COLORS.ERROR)
                .setTitle(`${EMOJIS.ERROR} Teste do MercadoPago - Falha`)
                .setDescription(`**Erro:** ${testResult.error}`)
                .addFields({
                    name: '📋 Detalhes',
                    value: testResult.details || 'Nenhum detalhe adicional disponível',
                    inline: false
                })
                .setTimestamp();

            // Adiciona informações de debug se disponíveis
            if (testResult.debugInfo) {
                const debugFields = [];

                if (testResult.debugInfo.statusCode) {
                    debugFields.push(`**Código HTTP:** ${testResult.debugInfo.statusCode}`);
                }

                if (testResult.debugInfo.statusText) {
                    debugFields.push(`**Status:** ${testResult.debugInfo.statusText}`);
                }

                if (testResult.debugInfo.errorType) {
                    debugFields.push(`**Tipo:** ${testResult.debugInfo.errorType}`);
                }

                if (debugFields.length > 0) {
                    embed.addFields({
                        name: '🔍 Informações Técnicas',
                        value: debugFields.join('\n'),
                        inline: false
                    });
                }

                if (testResult.debugInfo.suggestion) {
                    embed.addFields({
                        name: '💡 Sugestão',
                        value: testResult.debugInfo.suggestion,
                        inline: false
                    });
                }
            }

            // Adiciona sugestões baseadas no tipo de erro (fallback)
            if (!testResult.debugInfo?.suggestion) {
                if (testResult.error.includes('não configurado')) {
                    embed.addFields({
                        name: '💡 Solução',
                        value: 'Configure o MercadoPago usando o botão "MercadoPago" no comando `/configbot`.',
                        inline: false
                    });
                } else if (testResult.error.includes('inválido') || testResult.error.includes('401')) {
                    embed.addFields({
                        name: '💡 Solução',
                        value: 'Verifique se o Access Token está correto e não expirou. Tokens devem começar com "TEST-" (sandbox) ou "APP_USR-" (produção).',
                        inline: false
                    });
                } else if (testResult.error.includes('permissões') || testResult.error.includes('403')) {
                    embed.addFields({
                        name: '💡 Solução',
                        value: 'Verifique se o token tem permissões para criar pagamentos PIX no painel do MercadoPago.',
                        inline: false
                    });
                } else if (testResult.error.includes('rede') || testResult.error.includes('conectividade')) {
                    embed.addFields({
                        name: '💡 Solução',
                        value: 'Verifique sua conexão com a internet e tente novamente. Se o problema persistir, pode ser um problema temporário da API.',
                        inline: false
                    });
                } else if (testResult.error.includes('timeout')) {
                    embed.addFields({
                        name: '💡 Solução',
                        value: 'A API está demorando para responder. Aguarde alguns minutos e tente novamente.',
                        inline: false
                    });
                }
            }
        }

        await interaction.editReply({
            embeds: [embed]
        });

        // Log do resultado do teste
        logger.info(`Teste do MercadoPago concluído para guild ${interaction.guild.id}:`, {
            success: testResult.success,
            environment: testResult.environment,
            error: testResult.error || 'Nenhum erro'
        });

    } catch (error) {
        logger.error('Erro ao testar configuração do MercadoPago:', error);

        const errorEmbed = new EmbedBuilder()
            .setColor(COLORS.ERROR)
            .setTitle(`${EMOJIS.ERROR} Erro no Teste`)
            .setDescription('Ocorreu um erro interno ao testar a configuração do MercadoPago.')
            .addFields({
                name: '📋 Detalhes do Erro',
                value: error.message || 'Erro desconhecido',
                inline: false
            })
            .setTimestamp();

        if (interaction.deferred) {
            await interaction.editReply({ embeds: [errorEmbed] });
        } else {
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }
}
