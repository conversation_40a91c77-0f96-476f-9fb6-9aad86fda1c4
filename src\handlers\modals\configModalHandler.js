import { PermissionFlagsBits } from 'discord.js';
import { logger } from '../../utils/logger.js';
import { BotConfig } from '../../models/BotConfig.js';

/**
 * Handler para modais de configuração
 */
export class ConfigModalHandler {
    /**
     * Manipula modais de configuração
     * @param {Object} interaction - Interação do Discord
     */
    static async handleConfigModal(interaction) {
        try {
            // Verificação de permissões
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem usar configurações.',
                    ephemeral: true
                });
            }

            const customId = interaction.customId;
            logger.info(`Modal de configuração enviado: ${customId} por ${interaction.user.tag}`);

            if (customId === 'config_mercadopago_modal') {
                await this.handleMercadoPagoModal(interaction);
            } else if (customId === 'config_rate_limit_modal') {
                await this.handleRateLimitModal(interaction);
            } else if (customId === 'config_log_settings_modal') {
                await this.handleLogSettingsModal(interaction);
            } else {
                logger.warn(`Modal de configuração não reconhecido: ${customId}`);
                await interaction.reply({
                    content: '❌ Modal de configuração não reconhecido.',
                    ephemeral: true
                });
            }
        } catch (error) {
            logger.error('Erro no handler de modal de configuração:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar a configuração.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de configuração do MercadoPago
     * @param {Object} interaction - Interação do Discord
     */
    static async handleMercadoPagoModal(interaction) {
        try {
            const accessToken = interaction.fields.getTextInputValue('mp_access_token');
            const webhookUrl = interaction.fields.getTextInputValue('mp_webhook_url') || null;

            // Validação básica do access token
            if (!accessToken.startsWith('APP_USR-')) {
                return await interaction.reply({
                    content: '❌ Access Token inválido. Deve começar com "APP_USR-".',
                    ephemeral: true
                });
            }

            // Aqui você salvaria as configurações no banco de dados
            // const config = await BotConfig.findByGuild(interaction.guild.id);
            // await config.updateMercadoPagoConfig(accessToken, webhookUrl);

            await interaction.reply({
                content: '✅ Configurações do MercadoPago salvas com sucesso!',
                ephemeral: true
            });

            logger.info(`Configurações do MercadoPago atualizadas para guild ${interaction.guild.id}`);
        } catch (error) {
            logger.error('Erro ao salvar configurações do MercadoPago:', error);
            
            await interaction.reply({
                content: '❌ Erro ao salvar configurações do MercadoPago.',
                ephemeral: true
            });
        }
    }

    /**
     * Manipula modal de configuração de rate limiting
     * @param {Object} interaction - Interação do Discord
     */
    static async handleRateLimitModal(interaction) {
        try {
            const maxRequests = parseInt(interaction.fields.getTextInputValue('rl_max_requests'));
            const windowSize = parseInt(interaction.fields.getTextInputValue('rl_window_size'));

            // Validação dos valores
            if (isNaN(maxRequests) || maxRequests < 1 || maxRequests > 1000) {
                return await interaction.reply({
                    content: '❌ Número máximo de requisições deve ser entre 1 e 1000.',
                    ephemeral: true
                });
            }

            if (isNaN(windowSize) || windowSize < 10 || windowSize > 3600) {
                return await interaction.reply({
                    content: '❌ Janela de tempo deve ser entre 10 e 3600 segundos.',
                    ephemeral: true
                });
            }

            // Aqui você salvaria as configurações no banco de dados
            // const config = await BotConfig.findByGuild(interaction.guild.id);
            // await config.updateRateLimitConfig(maxRequests, windowSize);

            await interaction.reply({
                content: `✅ Rate limiting configurado: ${maxRequests} requisições por ${windowSize} segundos.`,
                ephemeral: true
            });

            logger.info(`Rate limiting atualizado para guild ${interaction.guild.id}: ${maxRequests}/${windowSize}s`);
        } catch (error) {
            logger.error('Erro ao salvar configurações de rate limiting:', error);
            
            await interaction.reply({
                content: '❌ Erro ao salvar configurações de rate limiting.',
                ephemeral: true
            });
        }
    }

    /**
     * Manipula modal de configurações de log
     * @param {Object} interaction - Interação do Discord
     */
    static async handleLogSettingsModal(interaction) {
        try {
            const logLevel = interaction.fields.getTextInputValue('log_level').toUpperCase();
            const enableFileLogsStr = interaction.fields.getTextInputValue('enable_file_logs').toLowerCase();

            // Validação do nível de log
            const validLogLevels = ['ERROR', 'WARN', 'INFO', 'DEBUG'];
            if (!validLogLevels.includes(logLevel)) {
                return await interaction.reply({
                    content: `❌ Nível de log inválido. Use: ${validLogLevels.join(', ')}`,
                    ephemeral: true
                });
            }

            // Validação do enable file logs
            const enableFileLogs = enableFileLogsStr === 'true' || enableFileLogsStr === '1';

            // Aqui você salvaria as configurações no banco de dados
            // const config = await BotConfig.findByGuild(interaction.guild.id);
            // await config.updateLogSettings(logLevel, enableFileLogs);

            await interaction.reply({
                content: `✅ Configurações de log salvas:\n• Nível: ${logLevel}\n• Logs em arquivo: ${enableFileLogs ? 'Habilitado' : 'Desabilitado'}`,
                ephemeral: true
            });

            logger.info(`Configurações de log atualizadas para guild ${interaction.guild.id}: ${logLevel}, files: ${enableFileLogs}`);
        } catch (error) {
            logger.error('Erro ao salvar configurações de log:', error);
            
            await interaction.reply({
                content: '❌ Erro ao salvar configurações de log.',
                ephemeral: true
            });
        }
    }

    /**
     * Manipula modal de configuração de webhook
     * @param {Object} interaction - Interação do Discord
     */
    static async handleWebhookConfigModal(interaction) {
        try {
            await interaction.reply({
                content: '🔗 Funcionalidade de configuração de webhook será implementada em breve!',
                ephemeral: true
            });
        } catch (error) {
            logger.error('Erro no handler de modal de webhook:', error);
            
            await interaction.reply({
                content: '❌ Erro ao processar configuração de webhook.',
                ephemeral: true
            });
        }
    }

    /**
     * Manipula modal de configuração de notificações
     * @param {Object} interaction - Interação do Discord
     */
    static async handleNotificationConfigModal(interaction) {
        try {
            await interaction.reply({
                content: '🔔 Funcionalidade de configuração de notificações será implementada em breve!',
                ephemeral: true
            });
        } catch (error) {
            logger.error('Erro no handler de modal de notificações:', error);
            
            await interaction.reply({
                content: '❌ Erro ao processar configuração de notificações.',
                ephemeral: true
            });
        }
    }

    /**
     * Manipula modal de configuração de segurança
     * @param {Object} interaction - Interação do Discord
     */
    static async handleSecurityConfigModal(interaction) {
        try {
            await interaction.reply({
                content: '🔒 Funcionalidade de configuração de segurança será implementada em breve!',
                ephemeral: true
            });
        } catch (error) {
            logger.error('Erro no handler de modal de segurança:', error);
            
            await interaction.reply({
                content: '❌ Erro ao processar configuração de segurança.',
                ephemeral: true
            });
        }
    }
}
