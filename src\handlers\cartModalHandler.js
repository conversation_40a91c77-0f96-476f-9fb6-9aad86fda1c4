import { logger } from '../utils/logger.js';
import ShoppingCart from '../models/ShoppingCart.js';
import { fixedCartEmbedHandler } from './fixedCartEmbedHandler.js';
import { getEmojiFromInteraction } from '../utils/emojiHelper.js';

/**
 * Handler para modais do carrinho de compras
 */
export class CartModalHandler {
    /**
     * Processa modal de gerenciamento de itens do carrinho
     * @param {Object} interaction - Interação do Discord
     */
    static async handleCartManageModal(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            // Busca carrinho ativo
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id, 
                interaction.guild.id
            );

            if (!cart || !cart.items || cart.items.length === 0) {
                const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                return await interaction.editReply({
                    content: `${errorEmoji} Nenhum carrinho ativo encontrado.`
                });
            }

            const changes = [];
            const errors = [];
            let hasChanges = false;

            // Processa cada campo do modal
            for (let i = 0; i < Math.min(cart.items.length, 5); i++) {
                const fieldId = `item_quantity_${i}`;
                const newQuantityStr = interaction.fields.getTextInputValue(fieldId);
                const newQuantity = parseInt(newQuantityStr);

                if (isNaN(newQuantity) || newQuantity < 0) {
                    const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                    errors.push(`${errorEmoji} Quantidade inválida para ${cart.items[i].productName}: "${newQuantityStr}"`);
                    continue;
                }

                const item = cart.items[i];
                const oldQuantity = item.quantity;

                if (newQuantity === 0) {
                    // Remove item
                    try {
                        await cart.removeItem(item.productId);
                        const trashEmoji = await getEmojiFromInteraction(interaction, 'TRASH');
                        changes.push(`${trashEmoji} **${item.productName}** removido do carrinho`);
                        hasChanges = true;
                    } catch (error) {
                        const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                        errors.push(`${errorEmoji} Erro ao remover ${item.productName}`);
                    }
                } else if (newQuantity !== oldQuantity) {
                    // Atualiza quantidade
                    try {
                        await cart.updateItemQuantity(item.productId, newQuantity);
                        const changeType = newQuantity > oldQuantity ? '📈' : '📉';
                        changes.push(`${changeType} **${item.productName}**: ${oldQuantity} → ${newQuantity}`);
                        hasChanges = true;
                    } catch (error) {
                        const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                        errors.push(`${errorEmoji} Erro ao atualizar ${item.productName}: ${error.message}`);
                    }
                }
            }

            // Atualiza embed se houve mudanças
            if (hasChanges) {
                // Recarrega carrinho para obter dados atualizados
                const updatedCart = await ShoppingCart.findById(cart._id);
                await fixedCartEmbedHandler.createOrUpdateCartEmbed(
                    interaction.channel,
                    updatedCart
                );
            }

            // Prepara resposta
            let response = '';

            if (changes.length > 0) {
                const successEmoji = await getEmojiFromInteraction(interaction, 'SUCCESS');
                response += `${successEmoji} **Carrinho atualizado com sucesso!**\n\n`;
                response += changes.join('\n') + '\n\n';
            }

            if (errors.length > 0) {
                if (changes.length > 0) {
                    const warningEmoji = await getEmojiFromInteraction(interaction, 'WARNING');
                    response += `${warningEmoji} **Alguns itens não puderam ser atualizados:**\n\n`;
                } else {
                    const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                    response += `${errorEmoji} **Erro ao atualizar carrinho:**\n\n`;
                }
                response += errors.join('\n');
            }

            if (changes.length === 0 && errors.length === 0) {
                const lightbulbEmoji = await getEmojiFromInteraction(interaction, 'LIGHTBULB');
                response = `${lightbulbEmoji} Nenhuma alteração foi feita no carrinho.`;
            }

            await interaction.editReply({
                content: response
            });

            logger.info(`Carrinho gerenciado pelo usuário ${interaction.user.tag} - ${changes.length} alterações, ${errors.length} erros`);

        } catch (error) {
            logger.error('Erro ao processar modal de gerenciamento:', error);
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            await interaction.editReply({
                content: `${errorEmoji} Erro ao processar alterações do carrinho.`
            });
        }
    }

    /**
     * Valida se uma quantidade é válida
     * @param {string} quantityStr - String da quantidade
     * @param {number} maxQuantity - Quantidade máxima permitida
     * @returns {Object} - Resultado da validação
     */
    static validateQuantity(quantityStr, maxQuantity = 99) {
        const quantity = parseInt(quantityStr);

        if (isNaN(quantity)) {
            return {
                valid: false,
                error: 'Quantidade deve ser um número'
            };
        }

        if (quantity < 0) {
            return {
                valid: false,
                error: 'Quantidade não pode ser negativa'
            };
        }

        if (quantity > maxQuantity) {
            return {
                valid: false,
                error: `Quantidade máxima: ${maxQuantity}`
            };
        }

        return {
            valid: true,
            quantity
        };
    }
}

export default CartModalHandler;
