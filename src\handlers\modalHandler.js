import { logger } from '../utils/logger.js';
import Product from '../models/Product.js';
import Store from '../models/Store.js';
import StockItem from '../models/StockItem.js';
import { StoreManager } from '../utils/storeManager.js';
import BotConfig from '../models/BotConfig.js';
import { BotLogger } from '../utils/botLogger.js';
import ShoppingCartHandler from './shoppingCartHandler.js';
import CartModalHandler from './cartModalHandler.js';
import { EmbedBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } from 'discord.js';
import { COLORS, VALIDATION } from '../config/constants.js';
import { getEmojiFromInteraction } from '../utils/emojiHelper.js';
import mongoose from 'mongoose';

/**
 * Manipula interações de modais
 * @param {ModalSubmitInteraction} interaction 
 */
export async function handleModal(interaction) {
    const customId = interaction.customId;
    
    try {
        logger.info(`Modal enviado: ${customId} por ${interaction.user.tag}`);

        // Roteamento baseado no customId do modal
        // Para modais de edição, o customId tem formato: store_edit_storeId
        if (customId.startsWith('store_edit_')) {
            const storeId = customId.replace('store_edit_', '');
            await handleStoreEditModal(interaction, storeId);
            return;
        }

        // Para modais de criação de produto, o customId tem formato: product_create_storeId
        if (customId.startsWith('product_create_')) {
            const storeId = customId.replace('product_create_', '');
            await handleProductCreateModal(interaction, storeId);
            return;
        }

        // Para modais de criação de estoque, o customId tem formato: stock_create_storeId_productId
        if (customId.startsWith('stock_create_')) {
            const parts = customId.replace('stock_create_', '').split('_');
            const storeId = parts[0];
            const productId = parts[1];
            await handleStockCreateModal(interaction, storeId, productId);
            return;
        }

        // Para modais de edição de item de estoque, o customId tem formato: edit_stock_item_modal_stockItemId
        if (customId.startsWith('edit_stock_item_modal_')) {
            const stockItemId = customId.replace('edit_stock_item_modal_', '');
            await handleEditStockItemModal(interaction, stockItemId);
            return;
        }

        // Emoji configuration is now handled via message collector, not modals

        // Para modais de busca no estoque, o customId tem formato: stock_search_modal_productId
        if (customId.startsWith('stock_search_modal_')) {
            const productId = customId.replace('stock_search_modal_', '');
            await handleStockSearchModal(interaction, productId);
            return;
        }

        // Para modais de quantidade do carrinho, o customId tem formato: cart_quantity_modal_productId
        if (customId.startsWith('cart_quantity_modal_')) {
            await ShoppingCartHandler.handleQuantityModal(interaction);
            return;
        }

        // Para modal de gerenciamento do carrinho fixo
        if (customId === 'cart_manage_modal') {
            await CartModalHandler.handleCartManageModal(interaction);
            return;
        }

        const [category, action] = customId.split('_');

        switch (category) {
            case 'store':
                await handleStoreModal(interaction, action);
                break;
            case 'admin':
                await handleAdminModal(interaction, action);
                break;
            case 'config':
                await handleConfigModal(interaction);
                break;
            default:
                logger.warn(`Categoria de modal não reconhecida: ${category}`);
                await interaction.reply({
                    content: '❌ Modal não reconhecido.',
                    ephemeral: true
                });
        }

    } catch (error) {
        logger.error(`Erro ao processar modal ${customId}:`, error);
        
        const errorMessage = {
            content: '❌ Erro ao processar o formulário.',
            ephemeral: true
        };

        try {
            if (interaction.replied) {
                await interaction.followUp(errorMessage);
            } else if (interaction.deferred) {
                await interaction.editReply(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        } catch (replyError) {
            logger.error(`Erro ao enviar resposta de erro para modal ${customId}:`, replyError);
        }
    }
}

/**
 * Manipula modais relacionados à loja
 */
async function handleStoreModal(interaction, action) {
    switch (action) {
        case 'create':
            await handleStoreCreateModal(interaction);
            break;
        case 'feedback':
            const feedback = interaction.fields.getTextInputValue('feedback_text');
            await interaction.reply({
                content: `📝 Obrigado pelo seu feedback: "${feedback}"`,
                ephemeral: true
            });
            break;
        case 'support':
            const issue = interaction.fields.getTextInputValue('issue_description');
            await interaction.reply({
                content: `🎫 Ticket de suporte criado! Descrição: "${issue}"`,
                ephemeral: true
            });
            break;
        default:
            await interaction.reply({
                content: '❌ Ação de modal de loja não reconhecida.',
                ephemeral: true
            });
    }
}

/**
 * Manipula o modal de criação de loja
 */
async function handleStoreCreateModal(interaction) {
    try {
        // Verificação se o usuário é administrador
        if (!interaction.member.permissions.has('Administrator')) {
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            return await interaction.reply({
                content: `${errorEmoji} Apenas administradores podem criar lojas.`,
                ephemeral: true
            });
        }

        // Defer a resposta pois a criação pode demorar
        await interaction.deferReply({ ephemeral: true });

        // Extrai os dados do modal
        const banner = interaction.fields.getTextInputValue('store_banner');
        const name = interaction.fields.getTextInputValue('store_name');
        const color = interaction.fields.getTextInputValue('store_color');
        const description = interaction.fields.getTextInputValue('store_description');

        // Validações básicas
        const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
        if (!StoreManager.isValidUrl(banner)) {
            return await interaction.editReply({
                content: `${errorEmoji} URL do banner inválida. Use uma URL válida de imagem (jpg, png, gif, webp).`
            });
        }

        if (!StoreManager.isValidColor(color)) {
            return await interaction.editReply({
                content: `${errorEmoji} Cor inválida. Use formato hex (#FFFFFF) ou nome de cor (red, blue, etc).`
            });
        }

        // Cria a loja
        const result = await StoreManager.createStore(interaction, {
            banner,
            name,
            color,
            description
        });

        if (result.success) {
            await interaction.editReply({
                content: `✅ Loja **${name}** criada com sucesso!\n` +
                        `📍 Canal: ${result.channel}\n` +
                        `🆔 ID da Loja: \`${result.store._id}\``
            });
        }

    } catch (error) {
        logger.error('Erro ao processar modal de criação de loja:', error);

        const errorMessage = error.message || 'Erro interno do servidor';
        const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');

        if (interaction.deferred) {
            await interaction.editReply({
                content: `${errorEmoji} Erro ao criar loja: ${errorMessage}`
            });
        } else {
            await interaction.reply({
                content: `${errorEmoji} Erro ao criar loja: ${errorMessage}`,
                ephemeral: true
            });
        }
    }
}

/**
 * Manipula modais administrativos
 */
async function handleAdminModal(interaction, action) {
    // Verificação básica de permissões
    if (!interaction.member.permissions.has('ADMINISTRATOR')) {
        return await interaction.reply({
            content: '❌ Você não tem permissão para usar este modal.',
            ephemeral: true
        });
    }

    switch (action) {
        case 'addproduct':
            const productName = interaction.fields.getTextInputValue('product_name');
            const productPrice = interaction.fields.getTextInputValue('product_price');
            
            await interaction.reply({
                content: `✅ Produto "${productName}" adicionado com preço R$ ${productPrice}`,
                ephemeral: true
            });
            break;
        default:
            await interaction.reply({
                content: '❌ Ação de modal administrativo não reconhecida.',
                ephemeral: true
            });
    }
}

/**
 * Manipula o modal de edição de loja
 */
async function handleStoreEditModal(interaction, storeId) {
    try {
        // Verificação se o usuário é administrador
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem editar lojas.',
                ephemeral: true
            });
        }

        // Defer a resposta pois a edição pode demorar
        await interaction.deferReply({ ephemeral: true });

        // Busca a loja no banco de dados
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.editReply({
                content: '❌ Loja não encontrada ou inativa.'
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.editReply({
                content: '❌ Esta loja não pertence a este servidor.'
            });
        }

        // Extrai os dados do modal
        const banner = interaction.fields.getTextInputValue('store_banner').trim();
        const name = interaction.fields.getTextInputValue('store_name').trim();
        const color = interaction.fields.getTextInputValue('store_color').trim();
        const description = interaction.fields.getTextInputValue('store_description').trim();

        // Objeto para armazenar apenas os campos que serão atualizados
        const updateData = {};
        let hasChanges = false;

        // Valida e adiciona banner se fornecido
        if (banner && banner !== store.banner) {
            if (!StoreManager.isValidUrl(banner)) {
                return await interaction.editReply({
                    content: '❌ URL do banner inválida. Use uma URL válida de imagem (jpg, png, gif, webp).'
                });
            }
            updateData.banner = banner;
            hasChanges = true;
        }

        // Valida e adiciona nome se fornecido
        if (name && name !== store.name) {
            // Verifica se já existe uma loja com o mesmo nome no servidor (exceto a atual)
            const existingStore = await Store.findOne({
                guildId: interaction.guild.id,
                name: name.toLowerCase(),
                isActive: true,
                _id: { $ne: storeId }
            });

            if (existingStore) {
                return await interaction.editReply({
                    content: '❌ Já existe uma loja com este nome no servidor.'
                });
            }

            updateData.name = name;
            hasChanges = true;
        }

        // Valida e adiciona cor se fornecida
        if (color && color !== store.color) {
            if (!StoreManager.isValidColor(color)) {
                return await interaction.editReply({
                    content: '❌ Cor inválida. Use formato hex (#FFFFFF) ou nome de cor (red, blue, etc).'
                });
            }
            updateData.color = color;
            hasChanges = true;
        }

        // Adiciona descrição se fornecida
        if (description && description !== store.description) {
            updateData.description = description;
            hasChanges = true;
        }

        // Verifica se há mudanças para aplicar
        if (!hasChanges) {
            return await interaction.editReply({
                content: '❌ Nenhuma alteração foi detectada. Os valores fornecidos são idênticos aos atuais.'
            });
        }

        // Adiciona metadados de modificação
        updateData.lastModifiedBy = interaction.user.id;

        // Atualiza a loja no banco de dados
        const updatedStore = await Store.findByIdAndUpdate(storeId, updateData, { new: true });

        // Atualiza o embed da loja no canal
        await StoreManager.updateStoreEmbed(updatedStore, interaction.guild);

        // Lista as alterações feitas
        const changes = [];
        if (updateData.banner) changes.push('🖼️ Banner');
        if (updateData.name) changes.push('📝 Nome');
        if (updateData.color) changes.push('🎨 Cor');
        if (updateData.description) changes.push('📄 Descrição');

        await interaction.editReply({
            content: `✅ Loja **${updatedStore.name}** editada com sucesso!\n\n` +
                    `**Alterações aplicadas:**\n${changes.join('\n')}\n\n` +
                    `📍 Canal: <#${updatedStore.channelId}>\n` +
                    `🆔 ID da Loja: \`${updatedStore._id}\``
        });

        logger.info(`Loja "${updatedStore.name}" editada por ${interaction.user.tag} em ${interaction.guild.name}. Alterações: ${changes.join(', ')}`);

    } catch (error) {
        logger.error('Erro ao editar loja:', error);

        const errorMessage = {
            content: '❌ Erro interno ao editar a loja. Tente novamente mais tarde.'
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(errorMessage);
        } else {
            await interaction.reply({ ...errorMessage, ephemeral: true });
        }
    }
}

/**
 * Manipula o modal de criação de produto
 */
async function handleProductCreateModal(interaction, storeId) {
    try {
        // Verificação se o usuário é administrador
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem criar produtos.',
                ephemeral: true
            });
        }

        // Defer a resposta pois a criação pode demorar
        await interaction.deferReply({ ephemeral: true });

        // Busca a loja no banco de dados
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.editReply({
                content: '❌ Loja não encontrada ou inativa.'
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.editReply({
                content: '❌ Esta loja não pertence a este servidor.'
            });
        }

        // Extrai os dados do modal
        const name = interaction.fields.getTextInputValue('product_name');
        const priceInput = interaction.fields.getTextInputValue('product_price');
        const emoji = interaction.fields.getTextInputValue('product_emoji') || null;

        // Validação do preço
        const price = parseFloat(priceInput.replace(',', '.'));

        if (isNaN(price) || price <= 0) {
            return await interaction.editReply({
                content: '❌ Valor inválido. Digite um número positivo (ex: 29.99 ou 50).'
            });
        }

        // Validação do emoji (se fornecido)
        if (emoji && emoji.trim()) {
            const emojiRegex = /^(\p{Emoji}|<a?:\w+:\d+>)$/u;
            if (!emojiRegex.test(emoji.trim())) {
                return await interaction.editReply({
                    content: '❌ Emoji inválido. Use um emoji Unicode (🎮) ou um emoji customizado (<:nome:id>).'
                });
            }
        }

        // Cria o produto no banco de dados
        const productData = {
            name: name.trim(),
            description: `Produto da loja ${store.name}`, // Descrição padrão
            price: price,
            stock: 0, // Produto criado sem estoque
            category: 'digital', // Categoria padrão
            status: 'out_of_stock', // Status inicial sem estoque
            createdBy: interaction.user.id,
            emoji: emoji ? emoji.trim() : null,
            storeId: storeId // Adiciona referência à loja
        };

        // Validação adicional dos dados antes de salvar
        if (!productData.name || productData.name.length === 0) {
            return await interaction.editReply({
                content: '❌ Nome do produto é obrigatório.'
            });
        }

        if (!productData.storeId || !mongoose.Types.ObjectId.isValid(productData.storeId)) {
            return await interaction.editReply({
                content: '❌ ID da loja inválido. Tente selecionar a loja novamente.'
            });
        }

        if (!productData.createdBy || productData.createdBy.length === 0) {
            return await interaction.editReply({
                content: '❌ Erro interno: usuário criador não identificado.'
            });
        }

        const product = new Product(productData);
        await product.save();

        // Mensagem de sucesso
        const successMessage = `✅ Produto criado com sucesso!\n\n` +
                              `**📦 Nome:** ${product.name}\n` +
                              `**💰 Valor:** R$ ${product.price.toFixed(2).replace('.', ',')}\n` +
                              `**${product.emoji || '📦'} Emoji:** ${product.emoji || 'Nenhum'}\n` +
                              `**🏪 Loja:** ${store.name}\n` +
                              `**📊 Status:** Sem estoque (use comandos de estoque para adicionar)\n\n` +
                              `🆔 **ID do Produto:** \`${product._id}\``;

        await interaction.editReply({
            content: successMessage
        });

        logger.info(`Produto "${product.name}" criado por ${interaction.user.tag} na loja "${store.name}" (${interaction.guild.name})`);
        
        // Log da criação do produto
        await BotLogger.logProductCreated(interaction.guild, interaction.user, product, store);

    } catch (error) {
        logger.error('Erro ao criar produto:', error);

        let errorMessage = '❌ Erro interno ao criar o produto. Tente novamente mais tarde.';

        // Tratamento específico para diferentes tipos de erro
        if (error.name === 'ValidationError') {
            errorMessage = '❌ Erro de validação nos dados do produto. Verifique se todos os campos estão corretos.';
        } else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
            if (error.code === 11000) {
                // Erro de chave duplicada
                if (error.message.includes('productId')) {
                    errorMessage = '❌ Erro de banco de dados: índice órfão detectado. Execute o script de correção: `node scripts/fix-productid-index.js`';
                } else {
                    errorMessage = '❌ Erro: dados duplicados detectados. Verifique se não existe um produto com o mesmo nome nesta loja.';
                }
            } else {
                errorMessage = '❌ Erro de conexão com o banco de dados. Tente novamente em alguns minutos.';
            }
        } else if (error.message.includes('ObjectId')) {
            errorMessage = '❌ Erro nos identificadores da loja. Tente selecionar a loja novamente.';
        } else if (error.message.includes('Cast to ObjectId failed')) {
            errorMessage = '❌ Erro de formato nos dados. Tente executar o comando novamente.';
        }

        const responseMessage = {
            content: errorMessage
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(responseMessage);
        } else {
            await interaction.reply({ ...responseMessage, ephemeral: true });
        }
    }
}

/**
 * Manipula modal de criação de estoque
 */
async function handleStockCreateModal(interaction, storeId, productId) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        // Validação básica dos IDs
        if (!productId || !storeId) {
            return await interaction.editReply({
                content: '❌ IDs de produto ou loja inválidos.'
            });
        }

        // Verifica se os IDs são válidos ObjectIds
        if (!mongoose.Types.ObjectId.isValid(productId) || !mongoose.Types.ObjectId.isValid(storeId)) {
            return await interaction.editReply({
                content: '❌ Formato de ID inválido. Tente selecionar o produto novamente.'
            });
        }

        // Busca o produto e a loja
        const product = await Product.findById(productId);
        const store = await Store.findById(storeId);

        if (!product || !store) {
            return await interaction.editReply({
                content: '❌ Produto ou loja não encontrados.'
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.editReply({
                content: '❌ Esta loja não pertence a este servidor.'
            });
        }

        // Verifica se o produto pertence à loja
        if (product.storeId.toString() !== storeId.toString()) {
            return await interaction.editReply({
                content: '❌ Este produto não pertence à loja selecionada.'
            });
        }

        // Obtém os dados do modal
        const stockLines = interaction.fields.getTextInputValue('stock_lines');
        const notes = interaction.fields.getTextInputValue('stock_notes') || null;

        // Log para debug
        logger.info(`Processando criação de estoque: ${stockLines.length} caracteres, ${stockLines.split('\n').length} linhas`);
        logger.debug(`Conteúdo do estoque: ${stockLines.substring(0, 200)}...`);

        // Validação básica do comprimento
        if (stockLines.length > VALIDATION.STOCK_CONTENT.MAX_TOTAL_LENGTH) {
            return await interaction.editReply({
                content: `❌ O texto de estoque é muito longo. Máximo permitido: ${VALIDATION.STOCK_CONTENT.MAX_TOTAL_LENGTH} caracteres.`
            });
        }

        // Processa as linhas de estoque
        const lines = stockLines.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        if (lines.length === 0) {
            return await interaction.editReply({
                content: '❌ Nenhuma linha de estoque válida foi fornecida.'
            });
        }

        // Validação adicional: verifica se alguma linha é muito longa
        const longLines = lines.filter(line => line.length > VALIDATION.STOCK_CONTENT.MAX_LINE_LENGTH);
        if (longLines.length > 0) {
            return await interaction.editReply({
                content: `❌ Algumas linhas de estoque são muito longas (máximo ${VALIDATION.STOCK_CONTENT.MAX_LINE_LENGTH} caracteres por linha). Verifique e tente novamente.`
            });
        }

        // Cria os itens de estoque
        const stockItems = [];
        const createdBy = interaction.user.id;

        // Converte IDs para ObjectId se necessário
        const productObjectId = mongoose.Types.ObjectId.isValid(productId) ?
            mongoose.Types.ObjectId.createFromHexString(productId) : productId;
        const storeObjectId = mongoose.Types.ObjectId.isValid(storeId) ?
            mongoose.Types.ObjectId.createFromHexString(storeId) : storeId;

        for (const line of lines) {
            const stockItem = new StockItem({
                productId: productObjectId,
                storeId: storeObjectId,
                content: line,
                status: 'available',
                createdBy: createdBy,
                notes: notes
            });

            stockItems.push(stockItem);
        }

        // Salva todos os itens de estoque
        await StockItem.insertMany(stockItems);

        // Atualiza o contador de estoque do produto
        const newStockCount = await StockItem.countByProduct(productObjectId, 'available');

        // Atualiza o produto com o novo estoque e status
        product.stock = newStockCount;
        if (newStockCount > 0 && product.status === 'out_of_stock') {
            product.status = 'active';
        }
        product.lastModifiedBy = createdBy;
        await product.save();

        // Mensagem de sucesso
        const successMessage = `✅ **Estoque adicionado com sucesso!**\n\n` +
                              `📦 **Produto:** ${product.name}\n` +
                              `🏪 **Loja:** ${store.name}\n` +
                              `📊 **Itens adicionados:** ${stockItems.length}\n` +
                              `📈 **Estoque total:** ${newStockCount}\n` +
                              `🔄 **Status:** ${product.status === 'active' ? '✅ Ativo' : '⚠️ ' + product.status}`;

        await interaction.editReply({
            content: successMessage
        });

        logger.info(`${stockItems.length} itens de estoque adicionados ao produto "${product.name}" por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao criar estoque:', error);
        logger.error('Stack trace:', error.stack);
        logger.error('Error details:', {
            name: error.name,
            message: error.message,
            code: error.code,
            productId,
            storeId,
            userId: interaction.user.id
        });

        let errorMessage = '❌ Erro interno ao adicionar estoque. Tente novamente mais tarde.';

        // Mensagens de erro mais específicas baseadas no tipo de erro
        if (error.name === 'ValidationError') {
            errorMessage = '❌ Erro de validação nos dados do estoque. Verifique o formato dos dados.';
        } else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
            errorMessage = '❌ Erro de conexão com o banco de dados. Tente novamente em alguns minutos.';
        } else if (error.message.includes('ObjectId')) {
            errorMessage = '❌ Erro nos identificadores do produto ou loja. Tente selecionar novamente.';
        } else if (error.message.includes('duplicate')) {
            errorMessage = '❌ Erro: dados duplicados detectados.';
        }

        const responseMessage = {
            content: errorMessage
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(responseMessage);
        } else {
            await interaction.reply({ ...responseMessage, ephemeral: true });
        }
    }
}

/**
 * Manipula modais de configuração do bot
 * @param {import('discord.js').ModalSubmitInteraction} interaction 
 */
async function handleConfigModal(interaction) {
    try {
        const customId = interaction.customId;
        
        // Verificação de permissão
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem usar esta configuração.',
                ephemeral: true
            });
        }

        if (customId === 'config_mercadopago_modal') {
            await handleMercadoPagoModal(interaction);
        } else if (customId === 'config_rate_limit_modal') {
            await handleRateLimitModal(interaction);
        } else if (customId === 'config_log_settings_modal') {
            await handleLogSettingsModal(interaction);
        } else {
            logger.warn(`Modal de configuração não reconhecido: ${customId}`);
            await interaction.reply({
                content: '❌ Modal de configuração não reconhecido.',
                ephemeral: true
            });
        }
    } catch (error) {
        logger.error('Erro no handler de modal de configuração:', error);
        
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({
                content: '❌ Ocorreu um erro ao processar a configuração.',
                ephemeral: true
            });
        }
    }
}

/**
 * Processa modal de configuração do MercadoPago
 */
async function handleMercadoPagoModal(interaction) {
    const accessToken = interaction.fields.getTextInputValue('mp_access_token');
    const publicKey = interaction.fields.getTextInputValue('mp_public_key');
    const webhookSecret = interaction.fields.getTextInputValue('mp_webhook_secret') || null;

    // Validações aprimoradas de tokens
    const tokenValidation = validateMercadoPagoTokens(accessToken, publicKey);
    if (!tokenValidation.isValid) {
        const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
        return await interaction.reply({
            content: `${errorEmoji} ${tokenValidation.error}`,
            ephemeral: true
        });
    }

    try {
        // Busca ou cria configuração
        let config = await BotConfig.findByGuild(interaction.guild.id);
        if (!config) {
            config = new BotConfig({ guildId: interaction.guild.id });
        }

        // Atualiza configurações do MercadoPago
        await config.updateMercadoPago({
            accessToken,
            publicKey,
            webhookSecret,
            isEnabled: true
        }, interaction.user.id);

        const successEmoji = await getEmojiFromInteraction(interaction, 'SUCCESS');
        const embed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setTitle(`${successEmoji} MercadoPago Configurado`)
            .setDescription(`As configurações do MercadoPago foram salvas com sucesso!\n\n${tokenValidation.message}`)
            .addFields(
                {
                    name: '🔑 Access Token',
                    value: `\`${accessToken.substring(0, 20)}...\``,
                    inline: true
                },
                {
                    name: '🔓 Public Key',
                    value: `\`${publicKey.substring(0, 20)}...\``,
                    inline: true
                },
                {
                    name: '🌍 Ambiente',
                    value: tokenValidation.environment === 'sandbox' ? '🧪 Teste (Sandbox)' : '🚀 Produção',
                    inline: true
                },
                {
                    name: '🔗 Webhook',
                    value: webhookSecret ? '✅ Configurado' : '❌ Não configurado',
                    inline: true
                }
            )
            .setTimestamp();

        await interaction.reply({
            embeds: [embed],
            ephemeral: true
        });

        // Log da configuração
        await BotLogger.logConfigChanged(
            interaction.client,
            interaction.guild.id,
            interaction.user,
            'MercadoPago',
            'Configuração da API do MercadoPago atualizada'
        );

    } catch (error) {
        logger.error('Erro ao salvar configuração do MercadoPago:', error);
        
        const errorEmbed = new EmbedBuilder()
            .setColor(COLORS.ERROR)
            .setTitle(`${EMOJIS.ERROR} Erro`)
            .setDescription('Ocorreu um erro ao salvar as configurações do MercadoPago.')
            .setTimestamp();

        await interaction.reply({
            embeds: [errorEmbed],
            ephemeral: true
        });
    }
}

/**
 * Manipula modal de edição de item de estoque
 */
async function handleEditStockItemModal(interaction, stockItemId) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem editar itens de estoque.',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        // Busca o item de estoque
        const stockItem = await StockItem.findById(stockItemId).populate('productId');

        if (!stockItem) {
            return await interaction.editReply({
                content: '❌ Item de estoque não encontrado.'
            });
        }

        // Verifica se o item pertence ao servidor atual
        const store = await Store.findById(stockItem.storeId);
        if (!store || store.guildId !== interaction.guild.id) {
            return await interaction.editReply({
                content: '❌ Este item não pertence a este servidor.'
            });
        }

        // Obtém os dados do modal
        const newContent = interaction.fields.getTextInputValue('content').trim();
        const newNotes = interaction.fields.getTextInputValue('notes').trim() || null;

        // Validações
        if (!newContent) {
            return await interaction.editReply({
                content: '❌ O conteúdo do item não pode estar vazio.'
            });
        }

        if (newContent.length > VALIDATION.STOCK_CONTENT.MAX_LINE_LENGTH) {
            return await interaction.editReply({
                content: `❌ O conteúdo é muito longo. Máximo permitido: ${VALIDATION.STOCK_CONTENT.MAX_LINE_LENGTH} caracteres.`
            });
        }

        // Verifica se houve mudanças
        const hasContentChange = newContent !== stockItem.content;
        const hasNotesChange = newNotes !== stockItem.notes;

        if (!hasContentChange && !hasNotesChange) {
            return await interaction.editReply({
                content: '❌ Nenhuma alteração foi detectada.'
            });
        }

        // Atualiza o item
        const updateData = {
            lastModifiedBy: interaction.user.id
        };

        if (hasContentChange) {
            updateData.content = newContent;
        }

        if (hasNotesChange) {
            updateData.notes = newNotes;
        }

        await StockItem.findByIdAndUpdate(stockItemId, updateData);

        // Lista as alterações feitas
        const changes = [];
        if (hasContentChange) changes.push('📝 Conteúdo');
        if (hasNotesChange) changes.push('📋 Observações');

        const successMessage = `✅ **Item de estoque editado com sucesso!**\n\n` +
                              `📦 **Produto:** ${stockItem.productId.name}\n` +
                              `🏪 **Loja:** ${store.name}\n` +
                              `🔄 **Alterações:** ${changes.join(', ')}\n\n` +
                              `🆔 **ID do Item:** \`${stockItemId}\``;

        await interaction.editReply({
            content: successMessage
        });

        // Log da ação
        BotLogger.logAction(
            interaction.guild,
            interaction.user,
            'Edição de Item',
            `Item de estoque editado: ${stockItem.productId.name} - Alterações: ${changes.join(', ')}`
        );

        logger.info(`Item de estoque editado por ${interaction.user.tag}: ${stockItem.productId.name} - ${changes.join(', ')}`);

    } catch (error) {
        logger.error('Erro ao editar item de estoque:', error);

        const errorMessage = {
            content: '❌ Erro interno ao editar o item de estoque. Tente novamente mais tarde.'
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(errorMessage);
        } else {
            await interaction.reply({ ...errorMessage, ephemeral: true });
        }
    }
}

/**
 * Processa modal de configuração de Rate Limiting
 */
async function handleRateLimitModal(interaction) {
    const maxRequestsStr = interaction.fields.getTextInputValue('rl_max_requests');
    const windowMsStr = interaction.fields.getTextInputValue('rl_window_ms');
    const enabledStr = interaction.fields.getTextInputValue('rl_enabled').toLowerCase();

    // Validações
    const maxRequests = parseInt(maxRequestsStr);
    const windowMs = parseInt(windowMsStr);
    const isEnabled = enabledStr === 'true' || enabledStr === '1' || enabledStr === 'sim';

    if (isNaN(maxRequests) || maxRequests < 1 || maxRequests > 100) {
        return await interaction.reply({
            content: '❌ Número máximo de requisições deve ser entre 1 e 100.',
            ephemeral: true
        });
    }

    if (isNaN(windowMs) || windowMs < 1000 || windowMs > 3600000) {
        return await interaction.reply({
            content: '❌ Janela de tempo deve ser entre 1000ms (1s) e 3600000ms (1h).',
            ephemeral: true
        });
    }

    try {
        // Busca ou cria configuração
        let config = await BotConfig.findByGuild(interaction.guild.id);
        if (!config) {
            config = new BotConfig({ guildId: interaction.guild.id });
        }

        // Atualiza configurações de rate limiting
        await config.updateRateLimiting({
            windowMs,
            maxRequests,
            isEnabled
        }, interaction.user.id);

        const embed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setTitle(`${EMOJIS.SUCCESS} Rate Limiting Configurado`)
            .setDescription('As configurações de rate limiting foram salvas com sucesso!')
            .addFields(
                {
                    name: '⚡ Status',
                    value: isEnabled ? '✅ Ativado' : '❌ Desativado',
                    inline: true
                },
                {
                    name: '📊 Limite',
                    value: `${maxRequests} requisições`,
                    inline: true
                },
                {
                    name: '⏱️ Janela',
                    value: `${windowMs / 1000}s`,
                    inline: true
                }
            )
            .setTimestamp();

        await interaction.reply({
            embeds: [embed],
            ephemeral: true
        });

        // Log da configuração
        await BotLogger.logRateLimitHit(
            interaction.client,
            interaction.guild.id,
            interaction.user,
            'rate-limit-config'
        );

    } catch (error) {
        logger.error('Erro ao salvar configuração de rate limiting:', error);
        
        const errorEmbed = new EmbedBuilder()
            .setColor(COLORS.ERROR)
            .setTitle(`${EMOJIS.ERROR} Erro`)
            .setDescription('Ocorreu um erro ao salvar as configurações de rate limiting.')
            .setTimestamp();

        await interaction.reply({
            embeds: [errorEmbed],
            ephemeral: true
        });
    }
}

/**
 * Manipula modal de busca no estoque
 */
async function handleStockSearchModal(interaction, productId) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem buscar no estoque.',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        // Busca o produto
        const product = await Product.findById(productId).populate('storeId');

        if (!product) {
            return await interaction.editReply({
                content: '❌ Produto não encontrado.'
            });
        }

        // Obtém os valores do modal
        const searchTerm = interaction.fields.getTextInputValue('search_term').trim();
        const statusFilter = interaction.fields.getTextInputValue('status_filter').trim().toLowerCase();

        if (!searchTerm) {
            return await interaction.editReply({
                content: '❌ Termo de busca não pode estar vazio.'
            });
        }

        // Valida o filtro de status se fornecido
        const validStatuses = ['available', 'sold', 'reserved', 'expired'];
        let statusQuery = {};

        if (statusFilter && !validStatuses.includes(statusFilter)) {
            return await interaction.editReply({
                content: '❌ Status inválido. Use: available, sold, reserved ou expired.'
            });
        }

        if (statusFilter) {
            statusQuery.status = statusFilter;
        }

        // Busca itens que contenham o termo de busca no conteúdo
        const searchQuery = {
            productId: productId,
            content: { $regex: searchTerm, $options: 'i' }, // Case insensitive
            ...statusQuery
        };

        const foundItems = await StockItem.find(searchQuery).sort({ createdAt: -1 });

        if (foundItems.length === 0) {
            return await interaction.editReply({
                content: `❌ Nenhum item encontrado com o termo "${searchTerm}"${statusFilter ? ` e status "${statusFilter}"` : ''}.`
            });
        }

        // Cria embed com resultados da busca
        const embed = new EmbedBuilder()
            .setTitle(`🔍 Resultados da Busca - ${product.name}`)
            .setDescription(`**Termo:** "${searchTerm}"${statusFilter ? `\n**Status:** ${statusFilter}` : ''}`)
            .setColor('#00ff00')
            .setTimestamp();

        // Agrupa resultados por status
        const resultsByStatus = {
            available: foundItems.filter(item => item.status === 'available'),
            sold: foundItems.filter(item => item.status === 'sold'),
            reserved: foundItems.filter(item => item.status === 'reserved'),
            expired: foundItems.filter(item => item.status === 'expired')
        };

        // Adiciona resumo dos resultados
        const summary = [];
        if (resultsByStatus.available.length > 0) summary.push(`🟢 ${resultsByStatus.available.length} disponível(is)`);
        if (resultsByStatus.sold.length > 0) summary.push(`🔴 ${resultsByStatus.sold.length} vendido(s)`);
        if (resultsByStatus.reserved.length > 0) summary.push(`🟡 ${resultsByStatus.reserved.length} reservado(s)`);
        if (resultsByStatus.expired.length > 0) summary.push(`⚫ ${resultsByStatus.expired.length} expirado(s)`);

        embed.addFields({
            name: '📊 Resumo dos Resultados',
            value: summary.join('\n') || 'Nenhum resultado',
            inline: true
        });

        // Mostra alguns exemplos dos itens encontrados (máximo 10)
        const itemsToShow = foundItems.slice(0, 10);
        const itemsList = itemsToShow.map((item, index) => {
            const statusEmoji = item.status === 'available' ? '🟢' :
                               item.status === 'sold' ? '🔴' :
                               item.status === 'reserved' ? '🟡' : '⚫';

            const content = item.content.length > 40 ?
                item.content.substring(0, 40) + '...' :
                item.content;

            const dateInfo = item.status === 'sold' && item.soldAt ?
                ` (vendido em ${new Date(item.soldAt).toLocaleDateString('pt-BR')})` :
                item.status === 'reserved' && item.reservedAt ?
                ` (reservado em ${new Date(item.reservedAt).toLocaleDateString('pt-BR')})` :
                '';

            return `${index + 1}. ${statusEmoji} \`${content}\`${dateInfo}`;
        }).join('\n');

        if (itemsList) {
            embed.addFields({
                name: `🔍 Itens Encontrados (${Math.min(10, foundItems.length)} de ${foundItems.length})`,
                value: itemsList,
                inline: false
            });
        }

        if (foundItems.length > 10) {
            embed.addFields({
                name: '📝 Nota',
                value: `Mostrando apenas os primeiros 10 resultados de ${foundItems.length} encontrados.`,
                inline: false
            });
        }

        await interaction.editReply({
            embeds: [embed]
        });

        logger.info(`Busca no estoque do produto ${product.name} realizada por ${interaction.user.tag}: "${searchTerm}" - ${foundItems.length} resultados`);

    } catch (error) {
        logger.error('Erro ao processar busca no estoque:', error);

        const errorMessage = {
            content: '❌ Erro interno do servidor. Tente novamente mais tarde.'
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(errorMessage);
        } else {
            await interaction.reply({ ...errorMessage, ephemeral: true });
        }
    }
}

/**
 * Manipula modal de configurações de log
 * @param {import('discord.js').ModalSubmitInteraction} interaction
 */
async function handleLogSettingsModal(interaction) {
    try {
        const logLevel = interaction.fields.getTextInputValue('log_level').toUpperCase();
        const stackTraceValue = interaction.fields.getTextInputValue('stack_trace').toLowerCase();

        // Validação do nível de log
        const validLogLevels = ['ERROR', 'WARN', 'INFO', 'DEBUG'];
        if (!validLogLevels.includes(logLevel)) {
            return await interaction.reply({
                content: `❌ Nível de log inválido. Use um dos seguintes: ${validLogLevels.join(', ')}`,
                ephemeral: true
            });
        }

        // Validação do stack trace
        const includeStackTrace = stackTraceValue === 'true';
        if (stackTraceValue !== 'true' && stackTraceValue !== 'false') {
            return await interaction.reply({
                content: '❌ Valor inválido para Stack Trace. Use "true" ou "false".',
                ephemeral: true
            });
        }

        // Busca ou cria configuração
        let config = await BotConfig.findByGuild(interaction.guild.id);
        if (!config) {
            config = new BotConfig({ guildId: interaction.guild.id });
        }

        // Inicializa logSettings se não existir
        if (!config.logSettings) {
            config.logSettings = {};
        }

        // Atualiza configurações
        config.logSettings.discordLogLevel = logLevel;
        config.logSettings.includeStackTrace = includeStackTrace;
        config.lastModifiedBy = interaction.user.id;

        await config.save();

        // Limpa cache do botLogger
        const { botLogger } = await import('../utils/botLogger.js');
        botLogger.clearConfigCache(interaction.guild.id);

        await logger.userAction('Configurações de log atualizadas', {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            logLevel,
            includeStackTrace,
            configuradoPor: interaction.user.tag
        });

        const embed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setTitle(`${EMOJIS.SUCCESS} Configurações de Log Atualizadas`)
            .setDescription('As configurações de log foram atualizadas com sucesso!')
            .addFields(
                {
                    name: '📊 Nível de Log Discord',
                    value: `\`${logLevel}\``,
                    inline: true
                },
                {
                    name: '🐛 Stack Trace',
                    value: includeStackTrace ? '✅ Habilitado' : '❌ Desabilitado',
                    inline: true
                },
                {
                    name: '🔧 Configurado por',
                    value: `${interaction.user}`,
                    inline: true
                }
            )
            .setTimestamp();

        await interaction.reply({
            embeds: [embed],
            ephemeral: true
        });

        // Log da configuração usando o sistema antigo para compatibilidade
        await BotLogger.logConfigChanged(
            interaction.client,
            interaction.guild.id,
            interaction.user,
            'Configurações de Log',
            `Nível: ${logLevel}, Stack Trace: ${includeStackTrace}`
        );

    } catch (error) {
        await logger.logStructured('ERROR', 'COMMAND', 'Erro ao configurar settings de log', {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            error: error.message,
            stack: error.stack
        });

        const errorEmbed = new EmbedBuilder()
            .setColor(COLORS.ERROR)
            .setTitle(`${EMOJIS.ERROR} Erro`)
            .setDescription('Ocorreu um erro ao salvar as configurações de log.')
            .setTimestamp();

        await interaction.reply({
            embeds: [errorEmbed],
            ephemeral: true
        });
    }
}

/**
 * Valida tokens do MercadoPago
 * @param {string} accessToken - Access token
 * @param {string} publicKey - Public key
 * @returns {Object} - Resultado da validação
 */
function validateMercadoPagoTokens(accessToken, publicKey) {
    // Verifica se os tokens não estão vazios
    if (!accessToken || !publicKey) {
        return {
            isValid: false,
            error: 'Access Token e Public Key são obrigatórios.'
        };
    }

    // Identifica o tipo de ambiente baseado nos tokens
    const isAccessTokenSandbox = accessToken.startsWith('TEST-');
    const isAccessTokenProduction = accessToken.startsWith('APP_USR-');
    const isPublicKeySandbox = publicKey.startsWith('TEST-');
    const isPublicKeyProduction = publicKey.startsWith('APP_USR-');

    // Verifica se os tokens são válidos
    if (!isAccessTokenSandbox && !isAccessTokenProduction) {
        return {
            isValid: false,
            error: 'Access Token inválido. Deve começar com "TEST-" (sandbox) ou "APP_USR-" (produção).'
        };
    }

    if (!isPublicKeySandbox && !isPublicKeyProduction) {
        return {
            isValid: false,
            error: 'Public Key inválido. Deve começar com "TEST-" (sandbox) ou "APP_USR-" (produção).'
        };
    }

    // Verifica se ambos os tokens são do mesmo ambiente
    if ((isAccessTokenSandbox && !isPublicKeySandbox) || (isAccessTokenProduction && !isPublicKeyProduction)) {
        return {
            isValid: false,
            error: 'Access Token e Public Key devem ser do mesmo ambiente (ambos sandbox ou ambos produção).'
        };
    }

    // Verifica se está usando produção em ambiente de desenvolvimento
    const isProduction = process.env.NODE_ENV === 'production';
    if (!isProduction && isAccessTokenProduction) {
        return {
            isValid: false,
            error: '⚠️ Atenção: Você está usando tokens de PRODUÇÃO em ambiente de desenvolvimento. Use tokens de sandbox (TEST-) para testes.',
            warning: true
        };
    }

    // Verifica se está usando sandbox em produção
    if (isProduction && isAccessTokenSandbox) {
        return {
            isValid: false,
            error: '⚠️ Atenção: Você está usando tokens de SANDBOX em ambiente de produção. Use tokens de produção (APP_USR-) para produção.',
            warning: true
        };
    }

    return {
        isValid: true,
        environment: isAccessTokenSandbox ? 'sandbox' : 'production',
        message: `Tokens válidos para ambiente ${isAccessTokenSandbox ? 'de teste (sandbox)' : 'de produção'}.`
    };
}
