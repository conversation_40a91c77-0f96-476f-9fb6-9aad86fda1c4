import { logger } from '../../utils/logger.js';

/**
 * <PERSON>ler para botões relacionados à loja
 */
export class StoreButtonHandler {
    /**
     * Manipula botões relacionados à loja
     * @param {Object} interaction - Interação do Discord
     * @param {string} action - Ação do botão
     * @param {string} id - ID relacionado ao botão
     */
    static async handleStoreButton(interaction, action, id) {
        try {
            logger.info(`Botão de loja clicado: ${action} (ID: ${id}) por ${interaction.user.tag}`);

            switch (action) {
                case 'buy':
                    await this.handleBuyButton(interaction, id);
                    break;
                case 'info':
                    await this.handleInfoButton(interaction, id);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Ação de loja não reconhecida.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            logger.error('Erro no handler de botão de loja:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar sua solicitação.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula botão de compra
     * @param {Object} interaction - Interação do Discord
     * @param {string} id - ID do produto
     */
    static async handleBuyButton(interaction, id) {
        await interaction.reply({
            content: `🛒 Funcionalidade de compra será implementada em breve! (ID: ${id})`,
            ephemeral: true
        });
    }

    /**
     * Manipula botão de informações
     * @param {Object} interaction - Interação do Discord
     * @param {string} id - ID do produto
     */
    static async handleInfoButton(interaction, id) {
        await interaction.reply({
            content: `ℹ️ Informações do produto serão exibidas aqui! (ID: ${id})`,
            ephemeral: true
        });
    }
}
