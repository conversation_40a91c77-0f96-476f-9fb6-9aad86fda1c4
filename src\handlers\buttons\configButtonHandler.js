import { 
    EmbedBuilder, 
    ActionRowBuilder, 
    ButtonBuilder, 
    ButtonStyle, 
    ModalBuilder, 
    TextInputBuilder, 
    TextInputStyle,
    ChannelSelectMenuBuilder,
    ChannelType,
    PermissionFlagsBits
} from 'discord.js';
import { logger } from '../../utils/logger.js';
import { COLORS, EMOJIS } from '../../config/constants.js';
import { BotConfig } from '../../models/BotConfig.js';

/**
 * Handler para botões de configuração
 */
export class ConfigButtonHandler {
    /**
     * Manipula botões de configuração
     * @param {Object} interaction - Interação do Discord
     * @param {string} action - Ação do botão
     * @param {string} id - ID relacionado ao botão
     */
    static async handleConfigButton(interaction, action, id) {
        try {
            // Verificação de permissão
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem usar esta configuração.',
                    ephemeral: true
                });
            }

            logger.info(`Botão de configuração clicado: ${action} por ${interaction.user.tag}`);

            switch (action) {
                case 'log_channels':
                    await this.handleLogChannelsConfig(interaction);
                    break;
                case 'log_settings':
                    await this.handleLogSettingsConfig(interaction);
                    break;
                case 'mercadopago':
                    await this.handleMercadoPagoConfig(interaction);
                    break;
                case 'rate_limit':
                    await this.handleRateLimitConfig(interaction);
                    break;
                case 'test_logs':
                    await this.handleTestLogs(interaction);
                    break;
                case 'test_mercadopago':
                    await this.handleTestMercadoPago(interaction);
                    break;
                case 'reset_all':
                    await this.handleResetConfig(interaction);
                    break;
                // Manter compatibilidade com botões antigos
                case 'admin_logs':
                    await this.handleAdminLogsConfig(interaction);
                    break;
                case 'public_logs':
                    await this.handlePublicLogsConfig(interaction);
                    break;
                case 'view_logs':
                    await this.handleViewLogs(interaction);
                    break;
                default:
                    logger.warn(`Ação de configuração não reconhecida: ${action}`);
                    await interaction.reply({
                        content: '❌ Configuração não reconhecida.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            logger.error('Erro no handler de configuração:', error);
            
            try {
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        content: '❌ Ocorreu um erro ao processar a configuração.',
                        ephemeral: true
                    });
                } else if (interaction.deferred) {
                    await interaction.editReply({
                        content: '❌ Ocorreu um erro ao processar a configuração.'
                    });
                } else {
                    await interaction.followUp({
                        content: '❌ Ocorreu um erro ao processar a configuração.',
                        ephemeral: true
                    });
                }
            } catch (replyError) {
                logger.error('Erro ao responder após falha no handler de configuração:', replyError);
            }
        }
    }

    /**
     * Configura canal de logs administrativos
     */
    static async handleAdminLogsConfig(interaction) {
        const embed = new EmbedBuilder()
            .setColor(COLORS.PRIMARY)
            .setTitle(`${EMOJIS.INFO} Configurar Canal de Logs Admin`)
            .setDescription('Selecione o canal onde serão enviados os logs administrativos do bot.');

        const selectMenu = new ChannelSelectMenuBuilder()
            .setCustomId('config_select_admin_channel')
            .setPlaceholder('Selecione um canal de texto')
            .setChannelTypes(ChannelType.GuildText);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.reply({
            embeds: [embed],
            components: [row],
            ephemeral: true
        });
    }

    /**
     * Configura canal de logs públicas
     */
    static async handlePublicLogsConfig(interaction) {
        const embed = new EmbedBuilder()
            .setColor(COLORS.PRIMARY)
            .setTitle(`${EMOJIS.INFO} Configurar Canal de Logs Públicas`)
            .setDescription('Selecione o canal onde serão enviados os logs públicos (vendas, etc.).');

        const selectMenu = new ChannelSelectMenuBuilder()
            .setCustomId('config_select_public_channel')
            .setPlaceholder('Selecione um canal de texto')
            .setChannelTypes(ChannelType.GuildText);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.reply({
            embeds: [embed],
            components: [row],
            ephemeral: true
        });
    }

    /**
     * Configura API do MercadoPago
     */
    static async handleMercadoPagoConfig(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('config_mercadopago_modal')
            .setTitle('Configurar MercadoPago');

        const accessTokenInput = new TextInputBuilder()
            .setCustomId('mp_access_token')
            .setLabel('Access Token')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('APP_USR-...')
            .setRequired(true);

        const webhookUrlInput = new TextInputBuilder()
            .setCustomId('mp_webhook_url')
            .setLabel('Webhook URL (opcional)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('https://seu-dominio.com/webhook')
            .setRequired(false);

        const firstActionRow = new ActionRowBuilder().addComponents(accessTokenInput);
        const secondActionRow = new ActionRowBuilder().addComponents(webhookUrlInput);

        modal.addComponents(firstActionRow, secondActionRow);

        await interaction.showModal(modal);
    }

    /**
     * Configura Rate Limiting
     */
    static async handleRateLimitConfig(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('config_rate_limit_modal')
            .setTitle('Configurar Rate Limiting');

        const maxRequestsInput = new TextInputBuilder()
            .setCustomId('rl_max_requests')
            .setLabel('Máximo de Requisições por Minuto')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('10')
            .setRequired(true);

        const windowSizeInput = new TextInputBuilder()
            .setCustomId('rl_window_size')
            .setLabel('Janela de Tempo (segundos)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('60')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(maxRequestsInput);
        const secondActionRow = new ActionRowBuilder().addComponents(windowSizeInput);

        modal.addComponents(firstActionRow, secondActionRow);

        await interaction.showModal(modal);
    }

    /**
     * Reset de todas as configurações
     */
    static async handleResetConfig(interaction) {
        const embed = new EmbedBuilder()
            .setColor(COLORS.WARNING)
            .setTitle(`${EMOJIS.WARNING} Confirmar Reset`)
            .setDescription('⚠️ **ATENÇÃO**: Esta ação irá resetar TODAS as configurações do bot neste servidor.\n\nIsso inclui:\n• Canais de logs\n• Configurações do MercadoPago\n• Rate limiting\n• Todas as outras configurações\n\n**Esta ação não pode ser desfeita!**');

        const confirmButton = new ButtonBuilder()
            .setCustomId('config_confirm_reset')
            .setLabel('Confirmar Reset')
            .setEmoji('🔄')
            .setStyle(ButtonStyle.Danger);

        const cancelButton = new ButtonBuilder()
            .setCustomId('config_cancel_reset')
            .setLabel('Cancelar')
            .setEmoji('❌')
            .setStyle(ButtonStyle.Secondary);

        const row = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

        await interaction.reply({
            embeds: [embed],
            components: [row],
            ephemeral: true
        });
    }

    /**
     * Configura canais de log estruturados
     */
    static async handleLogChannelsConfig(interaction) {
        const embed = new EmbedBuilder()
            .setColor(COLORS.PRIMARY)
            .setTitle(`${EMOJIS.INFO} Configurar Canais de Log`)
            .setDescription('Selecione o tipo de canal de log que deseja configurar:')
            .addFields(
                {
                    name: '🔧 Canais Principais',
                    value: '• **Admin** - Logs administrativos\n• **Público** - Logs públicos (vendas)\n• **Erro** - Logs de erro',
                    inline: true
                },
                {
                    name: '📊 Canais Específicos',
                    value: '• **Debug** - Logs de debug\n• **Performance** - Logs de performance\n• **Audit** - Logs de auditoria',
                    inline: true
                }
            );

        const adminButton = new ButtonBuilder()
            .setCustomId('config_admin_logs')
            .setLabel('Canal Admin')
            .setEmoji('🔧')
            .setStyle(ButtonStyle.Primary);

        const publicButton = new ButtonBuilder()
            .setCustomId('config_public_logs')
            .setLabel('Canal Público')
            .setEmoji('📢')
            .setStyle(ButtonStyle.Primary);

        const errorButton = new ButtonBuilder()
            .setCustomId('config_error_logs')
            .setLabel('Canal de Erro')
            .setEmoji('❌')
            .setStyle(ButtonStyle.Danger);

        const debugButton = new ButtonBuilder()
            .setCustomId('config_debug_logs')
            .setLabel('Canal Debug')
            .setEmoji('🐛')
            .setStyle(ButtonStyle.Secondary);

        const performanceButton = new ButtonBuilder()
            .setCustomId('config_performance_logs')
            .setLabel('Canal Performance')
            .setEmoji('📊')
            .setStyle(ButtonStyle.Secondary);

        const firstRow = new ActionRowBuilder().addComponents(adminButton, publicButton, errorButton);
        const secondRow = new ActionRowBuilder().addComponents(debugButton, performanceButton);

        await interaction.reply({
            embeds: [embed],
            components: [firstRow, secondRow],
            ephemeral: true
        });
    }

    /**
     * Configura configurações gerais de logging
     */
    static async handleLogSettingsConfig(interaction) {
        const config = await BotConfig.findByGuild(interaction.guild.id);

        const modal = new ModalBuilder()
            .setCustomId('config_log_settings_modal')
            .setTitle('Configurações de Log');

        const logLevelInput = new TextInputBuilder()
            .setCustomId('log_level')
            .setLabel('Nível de Log Discord (ERROR/WARN/INFO/DEBUG)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('INFO')
            .setValue(config?.logLevel || 'INFO')
            .setRequired(true);

        const enableFileLogsInput = new TextInputBuilder()
            .setCustomId('enable_file_logs')
            .setLabel('Habilitar Logs em Arquivo (true/false)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('true')
            .setValue(config?.enableFileLogs?.toString() || 'true')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(logLevelInput);
        const secondActionRow = new ActionRowBuilder().addComponents(enableFileLogsInput);

        modal.addComponents(firstActionRow, secondActionRow);

        await interaction.showModal(modal);
    }

    /**
     * Testa sistema de logs
     */
    static async handleTestLogs(interaction) {
        await interaction.reply({
            content: '🧪 Teste de logs será implementado em breve!',
            ephemeral: true
        });
    }

    /**
     * Testa configuração do MercadoPago
     */
    static async handleTestMercadoPago(interaction) {
        await interaction.reply({
            content: '🧪 Teste do MercadoPago será implementado em breve!',
            ephemeral: true
        });
    }

    /**
     * Visualiza logs atuais
     */
    static async handleViewLogs(interaction) {
        await interaction.reply({
            content: '📋 Visualização de logs será implementada em breve!',
            ephemeral: true
        });
    }
}
