import { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';
import { COLORS } from '../../config/constants.js';
import { emojiManager } from '../../utils/emojiManager.js';
import { logger } from '../../utils/logger.js';
import { getEmojiFromInteraction } from '../../utils/emojiHelper.js';

export default {
    data: new SlashCommandBuilder()
        .setName('emojis')
        .setDescription('Configura os emojis utilizados pelo bot (apenas administradores)'),
    
    async execute(interaction) {
        try {
            // Verificação se o usuário é administrador
            if (!interaction.member.permissions.has('Administrator')) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem configurar emojis.',
                    ephemeral: true
                });
            }

            // Obtém todos os emojis disponíveis
            const availableEmojis = emojiManager.getAvailableEmojis();
            const currentEmojis = await emojiManager.getAllEmojis(interaction.guild.id);

            // Agrupa emojis por categoria
            const categories = {};
            for (const [key, info] of Object.entries(availableEmojis)) {
                if (!categories[info.category]) {
                    categories[info.category] = [];
                }
                categories[info.category].push({
                    key,
                    ...info,
                    current: currentEmojis[key]
                });
            }

            // Cria embed principal
            const embed = new EmbedBuilder()
                .setColor(COLORS.PRIMARY)
                .setTitle('🎨 Configuração de Emojis')
                .setDescription('Selecione um emoji abaixo para personalizá-lo para este servidor.')
                .setThumbnail(interaction.client.user.displayAvatarURL())
                .setFooter({ 
                    text: `Servidor: ${interaction.guild.name}`,
                    iconURL: interaction.guild.iconURL()
                })
                .setTimestamp();

            // Adiciona campos por categoria
            for (const [categoryName, emojis] of Object.entries(categories)) {
                const emojiList = emojis.map(emoji => {
                    const isCustom = emoji.current !== emoji.emoji;
                    const status = isCustom ? '🔧' : '📋';
                    return `${status} ${emoji.current} **${emoji.key}** - ${emoji.description}`;
                }).join('\n');

                const categoryEmoji = await getCategoryEmoji(categoryName, interaction);
                embed.addFields({
                    name: `${categoryEmoji} ${categoryName}`,
                    value: emojiList,
                    inline: false
                });
            }

            // Adiciona legenda
            embed.addFields({
                name: '📖 Legenda',
                value: '📋 Emoji padrão\n🔧 Emoji personalizado',
                inline: false
            });

            // Cria select menu com todos os emojis
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('emoji_select')
                .setPlaceholder('Selecione um emoji para personalizar...')
                .setMinValues(1)
                .setMaxValues(1);

            // Adiciona opções organizadas por categoria
            for (const [categoryName, emojis] of Object.entries(categories)) {
                for (const emoji of emojis) {
                    const isCustom = emoji.current !== emoji.emoji;
                    const label = `${emoji.key} - ${emoji.description}`;
                    const description = `${categoryName} • Atual: ${emoji.current}${isCustom ? ' (personalizado)' : ' (padrão)'}`;
                    
                    selectMenu.addOptions(
                        new StringSelectMenuOptionBuilder()
                            .setLabel(label.length > 100 ? label.substring(0, 97) + '...' : label)
                            .setDescription(description.length > 100 ? description.substring(0, 97) + '...' : description)
                            .setValue(emoji.key)
                            .setEmoji(emoji.current)
                    );
                }
            }

            const row = new ActionRowBuilder().addComponents(selectMenu);

            await interaction.reply({
                embeds: [embed],
                components: [row],
                ephemeral: true
            });

            logger.info(`Comando emojis executado por ${interaction.user.tag} em ${interaction.guild.name}`);

        } catch (error) {
            logger.error('Erro no comando emojis:', error);
            
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            const errorEmbed = new EmbedBuilder()
                .setColor(COLORS.ERROR)
                .setTitle(`${errorEmoji} Erro`)
                .setDescription('Ocorreu um erro ao carregar as configurações de emojis.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};

/**
 * Retorna o emoji da categoria
 * @param {string} categoryName - Nome da categoria
 * @param {Interaction} interaction - Interação do Discord para obter emojis dinâmicos
 * @returns {Promise<string>} Emoji da categoria
 */
async function getCategoryEmoji(categoryName, interaction) {
    const categoryEmojis = {
        'Geral': await getEmojiFromInteraction(interaction, 'SETTINGS') || '⚙️',
        'Loja': await getEmojiFromInteraction(interaction, 'CART') || '🛒',
        'Navegação': await getEmojiFromInteraction(interaction, 'COMPASS') || '🧭',
        'Status': await getEmojiFromInteraction(interaction, 'CHART') || '📊'
    };
    return categoryEmojis[categoryName] || await getEmojiFromInteraction(interaction, 'FOLDER') || '📁';
}
