import { Events } from 'discord.js';
import { logger } from '../utils/logger.js';
import { handleSlashCommand } from '../handlers/slashCommandHandler.js';
import { handleAutocomplete } from '../handlers/autocompleteHandler.js';
import { handleButton } from '../handlers/buttonHandler.js';
import { handleSelectMenu } from '../handlers/selectMenuHandler.js';
import { handleModal } from '../handlers/modalHandler.js';
import { getEmoji } from '../utils/emojiHelper.js';

export default {
    name: Events.InteractionCreate,
    async execute(interaction) {
        const interactionType = interaction.type;
        const startTime = Date.now();

        try {
            // Log estruturado da interação
            await logger.event(`Interação recebida: ${interactionType}`, {
                guildId: interaction.guild?.id,
                userId: interaction.user.id,
                channel: interaction.channel?.id
            }, {
                type: interactionType,
                user: interaction.user.tag,
                guild: interaction.guild?.name || 'DM'
            });

            // Roteamento baseado no tipo de interação
            if (interaction.isChatInputCommand()) {
                await handleSlashCommand(interaction);
            } else if (interaction.isAutocomplete()) {
                await handleAutocomplete(interaction);
            } else if (interaction.isButton()) {
                await handleButton(interaction);
            } else if (interaction.isStringSelectMenu() || interaction.isChannelSelectMenu()) {
                await handleSelectMenu(interaction);
            } else if (interaction.isModalSubmit()) {
                await handleModal(interaction);
            } else {
                await logger.logStructured('WARN', 'EVENT', `Tipo de interação não suportado: ${interactionType}`, {
                    guildId: interaction.guild?.id,
                    userId: interaction.user.id
                }, {
                    interactionType,
                    customId: interaction.customId || 'N/A'
                });
            }

            const processingTime = Date.now() - startTime;

            // Log de performance se demorou muito
            if (processingTime > 3000) {
                await logger.performance(`Interação lenta processada: ${interactionType}`, {
                    guildId: interaction.guild?.id,
                    userId: interaction.user.id
                }, {
                    processingTime,
                    threshold: 3000
                });
            }

        } catch (error) {
            const processingTime = Date.now() - startTime;

            // Log estruturado do erro
            await logger.logStructured('ERROR', 'EVENT', 'Erro ao processar interação', {
                guildId: interaction.guild?.id,
                userId: interaction.user.id
            }, {
                error: error.message,
                stack: error.stack,
                interactionType,
                processingTime: `${processingTime}ms`,
                customId: interaction.customId || 'N/A'
            });

            // Tenta responder com erro se a interação ainda não foi respondida
            try {
                const errorEmoji = await getEmoji(interaction.guild?.id, 'ERROR');
                const errorMessage = {
                    content: `${errorEmoji} Ocorreu um erro ao processar sua solicitação. Tente novamente mais tarde.`,
                    ephemeral: true
                };

                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply(errorMessage);
                } else if (interaction.deferred) {
                    await interaction.editReply({
                        content: `${errorEmoji} Ocorreu um erro ao processar sua solicitação. Tente novamente mais tarde.`
                    });
                } else if (interaction.replied) {
                    await interaction.followUp(errorMessage);
                }
            } catch (replyError) {
                await logger.logStructured('ERROR', 'EVENT', 'Erro ao responder interação após falha', {
                    guildId: interaction.guild?.id,
                    userId: interaction.user.id
                }, {
                    originalError: error.message,
                    replyError: replyError.message,
                    interactionType
                });
            }
        }
    }
};
